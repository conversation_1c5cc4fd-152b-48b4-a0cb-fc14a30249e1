const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  // ลบข้อมูลเดิมในตาราง Food ก่อน
  await prisma.food.deleteMany();
  await prisma.food.createMany({
    data: [
      { name: 'ข้าวสวย', category: 'starch', calories: 80, protein: 2, carbs: 20, fat: 0, fiber: 0, standardUnit: 'ทัพพี' },
      { name: 'ไก่ต้ม', category: 'meat', calories: 55, protein: 7, carbs: 0, fat: 3, fiber: 0, standardUnit: 'ช้อนโต๊ะ' },
      { name: 'ผักบุ้งผัด', category: 'vegetable', calories: 25, protein: 2, carbs: 5, fat: 0, fiber: 1, standardUnit: 'ถ้วยตวง' },
      { name: 'กล้วยหอม', category: 'fruit', calories: 60, protein: 1, carbs: 15, fat: 0, fiber: 1, standardUnit: 'ลูก' },
      { name: 'น้ำมันพืช', category: 'fat', calories: 45, protein: 0, carbs: 0, fat: 5, fiber: 0, standardUnit: 'ช้อนชา' },
      { name: 'นมสด', category: 'milk', calories: 150, protein: 8, carbs: 12, fat: 8, fiber: 0, standardUnit: 'แก้ว' },
    ]
  });
  console.log('🌾 Seeded food data!');

  const foods = await prisma.food.findMany();
  console.log(foods);
}

console.log('Seed script started');
main().finally(() => {
  console.log('Seed script finished');
  prisma.$disconnect();
});

