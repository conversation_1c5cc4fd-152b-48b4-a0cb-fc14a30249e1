# 🧪 Session Management Testing Guide

## ระบบ Session Management ที่ได้พัฒนา

### ✅ **Features ที่เสร็จสิ้น:**

1. **Database Session Tracking**
   - UserSession table ในฐานข้อมูล
   - Session ID tracking
   - Device และ Browser detection
   - IP Address logging

2. **Concurrent Session Management**
   - จำกัดจำนวน active sessions ต่อผู้ใช้ (default: 3 sessions)
   - ตัด sessions เก่าออกอัตโนมัติเมื่อเกินจำนวนที่กำหนด
   - Session validation และ sync กับฐานข้อมูล

3. **API Endpoints**
   - `GET /api/sessions` - ดู active sessions ทั้งหมด
   - `DELETE /api/sessions?action=logout_others` - ออกจาก sessions อื่นทั้งหมด
   - `DELETE /api/sessions?action=logout_session&sessionId=xxx` - ออกจาก session ที่ระบุ
   - `DELETE /api/sessions?action=cleanup` - ทำความสะอาด expired sessions

4. **Session Security**
   - Session timeout (default: 8 ชั่วโมง)
   - Automatic cleanup expired sessions
   - Session invalidation ในฐานข้อมูล

## 🧪 **การทดสอบ**

### **Test Case 1: Basic Login และ Session Creation**
```bash
# 1. Login ครั้งแรก
curl -X POST http://localhost:3001/api/login \
  -H "Content-Type: application/json" \
  -H "User-Agent: Chrome/120.0 (Windows)" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "rememberMe": true
  }'

# 2. ตรวจสอบ session info
curl -X GET http://localhost:3001/api/session \
  -H "Cookie: nutrition-session=<session-cookie>"

# 3. ดู active sessions
curl -X GET http://localhost:3001/api/sessions \
  -H "Cookie: nutrition-session=<session-cookie>"
```

### **Test Case 2: Concurrent Sessions**
```bash
# 1. Login จาก device/browser ที่ 2
curl -X POST http://localhost:3001/api/login \
  -H "Content-Type: application/json" \
  -H "User-Agent: Firefox/119.0 (macOS)" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "rememberMe": false
  }'

# 2. Login จาก device/browser ที่ 3
curl -X POST http://localhost:3001/api/login \
  -H "Content-Type: application/json" \
  -H "User-Agent: Safari/17.0 (iOS)" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "rememberMe": false
  }'

# 3. Login จาก device/browser ที่ 4 (ควรตัด session เก่าออก)
curl -X POST http://localhost:3001/api/login \
  -H "Content-Type: application/json" \
  -H "User-Agent: Edge/120.0 (Windows)" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "rememberMe": false
  }'

# 4. ตรวจสอบว่ามี sessions เท่าไหร่
curl -X GET http://localhost:3001/api/sessions \
  -H "Cookie: nutrition-session=<session-cookie>"
```

### **Test Case 3: Force Logout Other Sessions**
```bash
# 1. Logout sessions อื่นทั้งหมด
curl -X DELETE "http://localhost:3001/api/sessions?action=logout_others" \
  -H "Cookie: nutrition-session=<session-cookie>"

# 2. ตรวจสอบว่าเหลือแค่ session ปัจจุบัน
curl -X GET http://localhost:3001/api/sessions \
  -H "Cookie: nutrition-session=<session-cookie>"
```

### **Test Case 4: Session Validation**
```bash
# 1. ตรวจสอบ session ที่ valid
curl -X GET http://localhost:3001/api/session \
  -H "Cookie: nutrition-session=<valid-session-cookie>"

# 2. ตรวจสอบ session ที่ invalid/expired
curl -X GET http://localhost:3001/api/session \
  -H "Cookie: nutrition-session=invalid-cookie"
```

## 📊 **Expected Results**

### **Concurrent Session Limit:**
- ผู้ใช้สามารถมี active sessions ได้สูงสุด 3 sessions
- เมื่อ login session ที่ 4 จะตัด session เก่าที่สุดออก

### **Session Information:**
```json
{
  "message": "ดึงข้อมูล active sessions สำเร็จ",
  "sessions": [
    {
      "id": "session-id-1",
      "userId": 1,
      "deviceInfo": {
        "deviceType": "desktop",
        "browserName": "Chrome",
        "osName": "Windows",
        "ipAddress": "*************"
      },
      "loginTime": "2024-01-01T10:00:00.000Z",
      "lastActivity": "2024-01-01T10:30:00.000Z",
      "isCurrent": true
    }
  ],
  "totalSessions": 1,
  "currentSessionId": "session-id-1"
}
```

### **Security Features:**
- Session timeout หลังจากไม่มีการใช้งาน 8 ชั่วโมง
- Session cleanup อัตโนมัติทุก 24 ชั่วโมง
- Session invalidation ในฐานข้อมูลเมื่อ logout

## 🎯 **การใช้งานจริง**

1. **Login Process**: ระบบจะสร้าง session record ในฐานข้อมูลและตัด sessions เก่าออกอัตโนมัติ
2. **Session Validation**: ทุก API call จะตรวจสอบ session validity กับฐานข้อมูล
3. **Device Management**: ผู้ใช้สามารถดูและจัดการ active sessions ได้
4. **Security**: ระบบป้องกัน concurrent sessions เกินจำนวนที่กำหนด

## 🔧 **Configuration**

Environment variables ที่สามารถปรับได้:
```env
MAX_CONCURRENT_SESSIONS=3
SESSION_TIMEOUT_HOURS=8
CLEANUP_INTERVAL_HOURS=24
SESSION_MAX_AGE=2592000000
```
