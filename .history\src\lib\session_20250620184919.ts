// src/lib/session.ts
import { getIronSession } from 'iron-session';
import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import { PrismaClient } from '@prisma/client';
import { UAParser } from 'ua-parser-js';

const prisma = new PrismaClient();

export interface SessionUser {
  id: number;
  email: string;
  fullName: string;
  gender: string;
  height: number;
  weight: number;
  activityLevel: string;
  goal: string;
  foodAllergies?: string;
  bmr?: number;
  tdee?: number;
  role: string;
  bmi: number;
  age: number;
  loginTime: number;
  lastActivity: number;
  sessionId?: string; // Add session ID for tracking
}

export interface DeviceInfo {
  userAgent: string;
  ipAddress: string;
  deviceType: string; // 'desktop', 'mobile', 'tablet'
  browserName: string;
  browserVersion: string;
  osName: string;
  osVersion: string;
  country?: string;
  city?: string;
}

export interface ActiveSession {
  id: string;
  userId: number;
  deviceInfo: DeviceInfo;
  loginTime: Date;
  lastActivity: Date;
  expiresAt: Date;
  isActive: boolean;
  isCurrent?: boolean;
}

export interface SavedCredential {
  email: string;
  password: string; // Encrypted password for autofill
  lastUsed: number;
  displayName?: string;
}

export interface SessionData {
  user?: SessionUser;
  isLoggedIn: boolean;
  sessionId?: string; // Add session ID to track this specific session
  loginHistory: Array<{
    email: string;
    loginTime: number;
    userAgent?: string;
  }>;
  savedCredentials: SavedCredential[];
}

// Session configuration
export const sessionOptions = {
  password: process.env.SESSION_SECRET || 'complex_password_at_least_32_characters_long_for_session_autofill_system_security',
  cookieName: 'nutrition-session',
  cookieOptions: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: parseInt(process.env.SESSION_MAX_AGE || '2592000000'), // 30 days
    sameSite: 'strict' as const,
  },
};

// Session management configuration
export const SESSION_CONFIG = {
  MAX_CONCURRENT_SESSIONS: parseInt(process.env.MAX_CONCURRENT_SESSIONS || '3'), // Max 3 active sessions per user
  SESSION_TIMEOUT_HOURS: parseInt(process.env.SESSION_TIMEOUT_HOURS || '8'), // 8 hours inactivity timeout
  CLEANUP_INTERVAL_HOURS: parseInt(process.env.CLEANUP_INTERVAL_HOURS || '24'), // Clean expired sessions every 24 hours
};

/**
 * Parse device information from request
 */
export function parseDeviceInfo(req: NextRequest): DeviceInfo {
  const userAgent = req.headers.get('user-agent') || 'Unknown';
  const parser = new UAParser(userAgent);
  const result = parser.getResult();

  // Get IP address (considering proxies)
  const forwarded = req.headers.get('x-forwarded-for');
  const ipAddress = forwarded ? forwarded.split(',')[0].trim() :
                   req.headers.get('x-real-ip') ||
                   req.ip ||
                   'Unknown';

  // Determine device type
  let deviceType = 'desktop';
  if (result.device.type === 'mobile') deviceType = 'mobile';
  else if (result.device.type === 'tablet') deviceType = 'tablet';

  return {
    userAgent,
    ipAddress,
    deviceType,
    browserName: result.browser.name || 'Unknown',
    browserVersion: result.browser.version || 'Unknown',
    osName: result.os.name || 'Unknown',
    osVersion: result.os.version || 'Unknown',
  };
}

/**
 * Create a new session record in database
 */
export async function createSessionRecord(
  userId: number,
  deviceInfo: DeviceInfo,
  sessionToken?: string
): Promise<string> {
  try {
    const expiresAt = new Date(Date.now() + parseInt(process.env.SESSION_MAX_AGE || '2592000000'));

    // Generate unique session token if not provided
    const finalSessionToken = sessionToken || crypto.randomUUID();

    // Check if session token already exists and handle duplicates
    let session;
    try {
      session = await prisma.userSession.create({
        data: {
          userId,
          sessionToken: finalSessionToken,
          userAgent: deviceInfo.userAgent,
          ipAddress: deviceInfo.ipAddress,
          deviceType: deviceInfo.deviceType,
          browserName: deviceInfo.browserName,
          osName: deviceInfo.osName,
          expiresAt,
          isActive: true,
        }
      });
    } catch (error: any) {
      if (error.code === 'P2002') {
        // Unique constraint violation - generate new token and try again
        const newSessionToken = crypto.randomUUID();
        session = await prisma.userSession.create({
          data: {
            userId,
            sessionToken: newSessionToken,
            userAgent: deviceInfo.userAgent,
            ipAddress: deviceInfo.ipAddress,
            deviceType: deviceInfo.deviceType,
            browserName: deviceInfo.browserName,
            osName: deviceInfo.osName,
            expiresAt,
            isActive: true,
          }
        });
      } else {
        throw error;
      }
    }

    console.log('✅ Session record created:', session.id);
    return session.id;
  } catch (error) {
    console.error('❌ Error creating session record:', error);
    throw error;
  }
}

/**
 * Get active sessions for a user
 */
export async function getActiveSessions(userId: number): Promise<ActiveSession[]> {
  try {
    const sessions = await prisma.userSession.findMany({
      where: {
        userId,
        isActive: true,
        expiresAt: {
          gt: new Date() // Not expired
        }
      },
      orderBy: {
        lastActivity: 'desc'
      }
    });

    return sessions.map(session => ({
      id: session.id,
      userId: session.userId,
      deviceInfo: {
        userAgent: session.userAgent || 'Unknown',
        ipAddress: session.ipAddress || 'Unknown',
        deviceType: session.deviceType || 'desktop',
        browserName: session.browserName || 'Unknown',
        browserVersion: 'Unknown',
        osName: session.osName || 'Unknown',
        osVersion: 'Unknown',
        country: session.country || undefined,
        city: session.city || undefined,
      },
      loginTime: session.loginTime,
      lastActivity: session.lastActivity,
      expiresAt: session.expiresAt,
      isActive: session.isActive,
    }));
  } catch (error) {
    console.error('❌ Error getting active sessions:', error);
    return [];
  }
}

/**
 * Enforce concurrent session limit
 */
export async function enforceConcurrentSessionLimit(userId: number, currentSessionId?: string): Promise<void> {
  try {
    const activeSessions = await getActiveSessions(userId);

    if (activeSessions.length >= SESSION_CONFIG.MAX_CONCURRENT_SESSIONS) {
      // Sort by last activity (oldest first)
      const sessionsToRemove = activeSessions
        .filter(session => session.id !== currentSessionId) // Don't remove current session
        .sort((a, b) => a.lastActivity.getTime() - b.lastActivity.getTime())
        .slice(0, activeSessions.length - SESSION_CONFIG.MAX_CONCURRENT_SESSIONS + 1);

      if (sessionsToRemove.length > 0) {
        await prisma.userSession.updateMany({
          where: {
            id: {
              in: sessionsToRemove.map(s => s.id)
            }
          },
          data: {
            isActive: false,
            logoutReason: 'concurrent_limit',
            updatedAt: new Date()
          }
        });

        console.log(`✅ Enforced session limit: removed ${sessionsToRemove.length} old sessions for user ${userId}`);
      }
    }
  } catch (error) {
    console.error('❌ Error enforcing session limit:', error);
  }
}

/**
 * Update session activity in database
 */
export async function updateSessionActivityInDB(sessionId: string): Promise<void> {
  try {
    await prisma.userSession.update({
      where: { id: sessionId },
      data: {
        lastActivity: new Date(),
        updatedAt: new Date()
      }
    });
  } catch (error) {
    console.error('❌ Error updating session activity in DB:', error);
  }
}

/**
 * Invalidate session in database
 */
export async function invalidateSessionInDB(sessionId: string, reason: string = 'manual'): Promise<void> {
  try {
    await prisma.userSession.update({
      where: { id: sessionId },
      data: {
        isActive: false,
        logoutReason: reason,
        updatedAt: new Date()
      }
    });
    console.log(`✅ Session ${sessionId} invalidated: ${reason}`);
  } catch (error) {
    console.error('❌ Error invalidating session in DB:', error);
  }
}

// Simple encryption for autofill passwords
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'autofill-key-32-characters-long-for-session-based-auth';

function encryptPassword(password: string): string {
  try {
    // Use a simple base64 encoding for autofill (not for production security)
    return Buffer.from(password, 'utf8').toString('base64');
  } catch (error) {
    console.error('Password encryption failed:', error);
    return password; // Return original if encryption fails
  }
}

function decryptPassword(encryptedPassword: string): string {
  try {
    // Decode from base64
    return Buffer.from(encryptedPassword, 'base64').toString('utf8');
  } catch (error) {
    console.error('Password decryption failed:', error);
    return '';
  }
}

/**
 * Get session from request
 */
export async function getSession(req: NextRequest): Promise<SessionData> {
  try {
    const session = await getIronSession<SessionData>(req, NextResponse.next(), sessionOptions);
    
    // Initialize session if empty
    if (!session.isLoggedIn) {
      session.isLoggedIn = false;
      session.loginHistory = session.loginHistory || [];
      session.savedCredentials = session.savedCredentials || [];
    }
    
    return session;
  } catch (error) {
    console.error('Error getting session:', error);
    return {
      isLoggedIn: false,
      loginHistory: [],
      savedCredentials: []
    };
  }
}

/**
 * Create session for user with concurrent session management
 */
export async function createSession(
  req: NextRequest,
  res: NextResponse,
  user: SessionUser,
  credentials: { email: string; password: string; rememberMe: boolean },
  userAgent?: string
): Promise<void> {
  try {
    const session = await getIronSession<SessionData>(req, res, sessionOptions);

    // Parse device information
    const deviceInfo = parseDeviceInfo(req);

    // Create session record in database
    const sessionId = await createSessionRecord(user.id, deviceInfo, session.id || '');

    // Enforce concurrent session limit (remove old sessions if needed)
    await enforceConcurrentSessionLimit(user.id, sessionId);

    // Set user data with session ID
    session.user = { ...user, sessionId };
    session.isLoggedIn = true;
    session.sessionId = sessionId;

    // Initialize arrays if not exists
    if (!session.loginHistory) {
      session.loginHistory = [];
    }
    if (!session.savedCredentials) {
      session.savedCredentials = [];
    }

    // Add to login history (keep last 5 logins เพื่อความเร็ว)
    const loginEntry = {
      email: user.email,
      loginTime: Date.now(),
      userAgent: deviceInfo.userAgent
    };

    session.loginHistory.unshift(loginEntry);
    session.loginHistory = session.loginHistory.slice(0, 5);

    // Save credentials for autofill if remember me is enabled
    if (credentials.rememberMe) {
      await saveCredentialsForAutofill(session, credentials.email, credentials.password, user.fullName);
    }

    await session.save();

    console.log('✅ Session created for user:', user.email, 'Session ID:', sessionId);
    console.log('📱 Device:', deviceInfo.deviceType, deviceInfo.browserName, deviceInfo.osName);
  } catch (error) {
    console.error('❌ Error creating session:', error);
    throw error;
  }
}

/**
 * Save credentials for autofill
 */
async function saveCredentialsForAutofill(
  session: SessionData, 
  email: string, 
  password: string, 
  displayName?: string
): Promise<void> {
  // Remove existing credential for this email
  session.savedCredentials = session.savedCredentials.filter(cred => cred.email !== email);
  
  // Add new credential
  const encryptedPassword = encryptPassword(password);
  const newCredential: SavedCredential = {
    email,
    password: encryptedPassword,
    lastUsed: Date.now(),
    displayName
  };
  
  session.savedCredentials.unshift(newCredential);

  // Keep only last 3 saved credentials เพื่อ<|im_start|>ความเร็ว
  session.savedCredentials = session.savedCredentials.slice(0, 3);
  
  console.log('✅ Credentials saved for autofill:', email);
}

/**
 * Get most recent saved credential for autofill
 */
export async function getMostRecentCredential(req: NextRequest): Promise<{
  email: string;
  password: string;
  displayName?: string;
} | null> {
  try {
    const session = await getSession(req);
    
    if (!session.savedCredentials || session.savedCredentials.length === 0) {
      return null;
    }
    
    // Sort by last used (most recent first)
    const sortedCredentials = session.savedCredentials.sort((a, b) => b.lastUsed - a.lastUsed);
    const mostRecent = sortedCredentials[0];
    
    return {
      email: mostRecent.email,
      password: decryptPassword(mostRecent.password),
      displayName: mostRecent.displayName
    };
  } catch (error) {
    console.error('Error getting most recent credential:', error);
    return null;
  }
}

/**
 * Get saved credentials for autofill
 */
export async function getSavedCredentials(req: NextRequest): Promise<Array<{
  email: string;
  password: string;
  displayName?: string;
  lastUsed: string;
}>> {
  try {
    const session = await getSession(req);

    if (!session.savedCredentials || session.savedCredentials.length === 0) {
      return [];
    }

    // Sort by last used (most recent first)
    const sortedCredentials = session.savedCredentials.sort((a, b) => b.lastUsed - a.lastUsed);

    return sortedCredentials.map(cred => ({
      email: cred.email,
      password: decryptPassword(cred.password),
      displayName: cred.displayName,
      lastUsed: new Date(cred.lastUsed).toLocaleString('th-TH')
    }));
  } catch (error) {
    console.error('Error getting saved credentials:', error);
    return [];
  }
}

/**
 * Clear saved credentials for specific email
 */
export async function clearSavedCredentials(req: NextRequest, res: NextResponse, email?: string): Promise<void> {
  try {
    const session = await getIronSession<SessionData>(req, res, sessionOptions);

    if (email) {
      // Remove credentials for specific email
      session.savedCredentials = session.savedCredentials.filter(cred => cred.email !== email);
      console.log('✅ Saved credentials cleared for email:', email);
    } else {
      // Clear all saved credentials
      session.savedCredentials = [];
      console.log('✅ All saved credentials cleared');
    }

    await session.save();
  } catch (error) {
    console.error('❌ Error clearing saved credentials:', error);
    throw error;
  }
}

/**
 * Update session activity
 */
export async function updateSessionActivity(req: NextRequest, res: NextResponse): Promise<void> {
  try {
    const session = await getIronSession<SessionData>(req, res, sessionOptions);

    if (session.user && session.sessionId) {
      session.user.lastActivity = Date.now();
      await session.save();

      // Also update in database
      await updateSessionActivityInDB(session.sessionId);
    }
  } catch (error) {
    console.error('❌ Error updating session activity:', error);
  }
}

/**
 * Destroy session
 */
export async function destroySession(req: NextRequest, res: NextResponse): Promise<void> {
  try {
    const session = await getIronSession<SessionData>(req, res, sessionOptions);

    // Invalidate session in database if session ID exists
    if (session.sessionId) {
      await invalidateSessionInDB(session.sessionId, 'manual');
    }

    // Keep login history and saved credentials but clear user data
    const loginHistory = session.loginHistory || [];
    const savedCredentials = session.savedCredentials || [];

    session.user = undefined;
    session.isLoggedIn = false;
    session.sessionId = undefined;
    session.loginHistory = loginHistory;
    session.savedCredentials = savedCredentials;

    await session.save();

    console.log('✅ Session destroyed');
  } catch (error) {
    console.error('❌ Error destroying session:', error);
    throw error;
  }
}

/**
 * Check if session is valid and not expired
 */
export async function isSessionValid(session: SessionData): Promise<boolean> {
  if (!session.isLoggedIn || !session.user) {
    return false;
  }

  const now = Date.now();
  const maxAge = parseInt(process.env.SESSION_MAX_AGE || '2592000000'); // 30 days
  const sessionAge = now - session.user.loginTime;

  // Check if session is expired
  if (sessionAge > maxAge) {
    console.log('❌ Session expired');
    return false;
  }

  // Check if user has been inactive for too long (8 hours)
  const maxInactivity = SESSION_CONFIG.SESSION_TIMEOUT_HOURS * 60 * 60 * 1000;
  const inactivityTime = now - (session.user.lastActivity || session.user.loginTime);

  if (inactivityTime > maxInactivity) {
    console.log('❌ Session inactive too long');
    return false;
  }

  // Check if session still exists and is active in database
  if (session.sessionId) {
    try {
      const dbSession = await prisma.userSession.findUnique({
        where: { id: session.sessionId }
      });

      if (!dbSession || !dbSession.isActive || dbSession.expiresAt < new Date()) {
        console.log('❌ Session invalidated in database');
        return false;
      }
    } catch (error) {
      console.error('❌ Error checking session in database:', error);
      return false;
    }
  }

  return true;
}

/**
 * Validate and sync session with database
 */
export async function validateAndSyncSession(req: NextRequest, res: NextResponse): Promise<SessionData | null> {
  try {
    const session = await getSession(req);

    if (!await isSessionValid(session)) {
      // Session is invalid, destroy it
      await destroySession(req, res);
      return null;
    }

    // Update activity if session is valid
    await updateSessionActivity(req, res);

    return session;
  } catch (error) {
    console.error('❌ Error validating session:', error);
    return null;
  }
}

/**
 * Force logout all other sessions for a user
 */
export async function forceLogoutOtherSessions(userId: number, currentSessionId: string): Promise<number> {
  try {
    const result = await prisma.userSession.updateMany({
      where: {
        userId,
        id: { not: currentSessionId },
        isActive: true
      },
      data: {
        isActive: false,
        logoutReason: 'forced_logout',
        updatedAt: new Date()
      }
    });

    console.log(`✅ Forced logout ${result.count} other sessions for user ${userId}`);
    return result.count;
  } catch (error) {
    console.error('❌ Error forcing logout other sessions:', error);
    return 0;
  }
}

/**
 * Cleanup expired sessions
 */
export async function cleanupExpiredSessions(): Promise<number> {
  try {
    const result = await prisma.userSession.updateMany({
      where: {
        OR: [
          { expiresAt: { lt: new Date() } },
          {
            isActive: true,
            lastActivity: {
              lt: new Date(Date.now() - SESSION_CONFIG.SESSION_TIMEOUT_HOURS * 60 * 60 * 1000)
            }
          }
        ]
      },
      data: {
        isActive: false,
        logoutReason: 'expired',
        updatedAt: new Date()
      }
    });

    console.log(`✅ Cleaned up ${result.count} expired sessions`);
    return result.count;
  } catch (error) {
    console.error('❌ Error cleaning up expired sessions:', error);
    return 0;
  }
}
