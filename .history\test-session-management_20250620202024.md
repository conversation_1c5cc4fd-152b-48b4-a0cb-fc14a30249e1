# 🧪 Session Management Testing Guide

## ระบบ Session Management ที่ได้พัฒนา

### ✅ **Features ที่เสร็จสิ้น:**

1. **Database Session Tracking**
   - UserSession table ในฐานข้อมูล
   - Session ID tracking
   - Device และ Browser detection
   - IP Address logging

2. **Single Session Enforcement (แก้ไขแล้ว)**
   - 🔒 **เมื่อล็อกอินจากอุปกรณ์ใหม่ → อุปกรณ์เก่าจะถูกบังคับออกจากระบบทันที**
   - ตรวจสอบ session validity กับฐานข้อมูลทุกครั้ง
   - Session จะหมดอายุทันทีเมื่อมีการล็อกอินที่อื่น

3. **API Endpoints**
   - `GET /api/sessions` - ดู active sessions ทั้งหมด
   - `DELETE /api/sessions?action=logout_others` - ออกจาก sessions อื่นทั้งหมด
   - `DELETE /api/sessions?action=logout_session&sessionId=xxx` - ออกจาก session ที่ระบุ
   - `DELETE /api/sessions?action=cleanup` - ทำความสะอาด expired sessions

4. **Session Security**
   - Session timeout (default: 8 ชั่วโมง)
   - Automatic cleanup expired sessions
   - Session invalidation ในฐานข้อมูล

## 🧪 **การทดสอบ**

### **Test Case 1: Basic Login และ Session Creation**
```bash
# 1. Login ครั้งแรก
curl -X POST http://localhost:3001/api/login \
  -H "Content-Type: application/json" \
  -H "User-Agent: Chrome/120.0 (Windows)" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "rememberMe": true
  }'

# 2. ตรวจสอบ session info
curl -X GET http://localhost:3001/api/session \
  -H "Cookie: nutrition-session=<session-cookie>"

# 3. ดู active sessions
curl -X GET http://localhost:3001/api/sessions \
  -H "Cookie: nutrition-session=<session-cookie>"
```

### **Test Case 2: Single Session Enforcement (แก้ไขแล้ว)**
```bash
# 1. Login จาก Browser ที่ 1 (Chrome)
curl -X POST http://localhost:3001/api/login \
  -H "Content-Type: application/json" \
  -H "User-Agent: Chrome/120.0 (Windows)" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "rememberMe": false
  }'
# → Session 1 สร้างสำเร็จ

# 2. ตรวจสอบ session จาก Browser ที่ 1
curl -X GET http://localhost:3001/api/session \
  -H "Cookie: nutrition-session=<session-1-cookie>"
# → ควรได้ 200 OK

# 3. Login จาก Browser ที่ 2 (Firefox) - ผู้ใช้คนเดียวกัน
curl -X POST http://localhost:3001/api/login \
  -H "Content-Type: application/json" \
  -H "User-Agent: Firefox/119.0 (macOS)" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "rememberMe": false
  }'
# → Session 2 สร้างสำเร็จ, Session 1 ถูกบังคับออกจากระบบ

# 4. ตรวจสอบ session จาก Browser ที่ 1 อีกครั้ง
curl -X GET http://localhost:3001/api/session \
  -H "Cookie: nutrition-session=<session-1-cookie>"
# → ควรได้ 401 Unauthorized (ถูกบังคับออกแล้ว)

# 5. ตรวจสอบ session จาก Browser ที่ 2
curl -X GET http://localhost:3001/api/session \
  -H "Cookie: nutrition-session=<session-2-cookie>"
# → ควรได้ 200 OK (session ปัจจุบัน)
```

### **Test Case 3: Force Logout Other Sessions**
```bash
# 1. Logout sessions อื่นทั้งหมด
curl -X DELETE "http://localhost:3001/api/sessions?action=logout_others" \
  -H "Cookie: nutrition-session=<session-cookie>"

# 2. ตรวจสอบว่าเหลือแค่ session ปัจจุบัน
curl -X GET http://localhost:3001/api/sessions \
  -H "Cookie: nutrition-session=<session-cookie>"
```

### **Test Case 4: Session Validation**
```bash
# 1. ตรวจสอบ session ที่ valid
curl -X GET http://localhost:3001/api/session \
  -H "Cookie: nutrition-session=<valid-session-cookie>"

# 2. ตรวจสอบ session ที่ invalid/expired
curl -X GET http://localhost:3001/api/session \
  -H "Cookie: nutrition-session=invalid-cookie"
```

## 📊 **Expected Results**

### **Single Session Enforcement:**
- ผู้ใช้สามารถมี active session ได้เพียง **1 session เท่านั้น**
- เมื่อ login จากอุปกรณ์ใหม่ → อุปกรณ์เก่าจะถูกบังคับออกจากระบบทันที
- Session เก่าจะได้รับ 401 Unauthorized ในการเรียก API ครั้งถัดไป

### **Session Information:**
```json
{
  "message": "ดึงข้อมูล active sessions สำเร็จ",
  "sessions": [
    {
      "id": "session-id-1",
      "userId": 1,
      "deviceInfo": {
        "deviceType": "desktop",
        "browserName": "Chrome",
        "osName": "Windows",
        "ipAddress": "*************"
      },
      "loginTime": "2024-01-01T10:00:00.000Z",
      "lastActivity": "2024-01-01T10:30:00.000Z",
      "isCurrent": true
    }
  ],
  "totalSessions": 1,
  "currentSessionId": "session-id-1"
}
```

### **Security Features:**
- Session timeout หลังจากไม่มีการใช้งาน 8 ชั่วโมง
- Session cleanup อัตโนมัติทุก 24 ชั่วโมง
- Session invalidation ในฐานข้อมูลเมื่อ logout

## 🎯 **การใช้งานจริง**

1. **Login Process**: ระบบจะสร้าง session record ในฐานข้อมูลและบังคับออกจาก sessions เก่าทั้งหมดทันที
2. **Session Validation**: ทุก API call จะตรวจสอบ session validity กับฐานข้อมูล
3. **Force Logout**: เมื่อล็อกอินจากอุปกรณ์ใหม่ อุปกรณ์เก่าจะได้รับ 401 Unauthorized ทันที
4. **Single Session Security**: ป้องกันการใช้งานพร้อมกันจากหลายอุปกรณ์

## 🔧 **Configuration**

Environment variables ที่สามารถปรับได้:
```env
SINGLE_SESSION_ONLY=true          # true = single session, false = multiple sessions
MAX_CONCURRENT_SESSIONS=1         # จำนวน sessions สูงสุด (ถ้า SINGLE_SESSION_ONLY=false)
SESSION_TIMEOUT_HOURS=8           # timeout หลังจากไม่ใช้งาน
CLEANUP_INTERVAL_HOURS=24         # ทำความสะอาด expired sessions
SESSION_MAX_AGE=2592000000        # อายุ session สูงสุด (30 วัน)
```

## 🔒 **การทำงานของ Single Session Enforcement**

1. **User A ล็อกอินจาก Computer** → Session 1 สร้าง ✅
2. **User A ล็อกอินจาก Phone** → Session 2 สร้าง, Session 1 ถูกบังคับออก ❌
3. **Computer พยายามเรียก API** → ได้รับ 401 Unauthorized
4. **Phone ใช้งานได้ปกติ** → Session 2 ยังคงใช้งานได้ ✅

**ผลลัพธ์**: ผู้ใช้สามารถล็อกอินได้จากอุปกรณ์เดียวเท่านั้น ถ้าล็อกอินจากอุปกรณ์ใหม่ อุปกรณ์เก่าจะถูกบังคับออกทันที

## 🚀 **Real-time Session Monitoring (ไม่ต้องกด Refresh)**

### ✅ **Features ที่เพิ่มขึ้น:**

1. **Auto Session Check**: ตรวจสอบ session validity ทุก 30 วินาที
2. **Real-time Logout Detection**: ตรวจจับการถูกบังคับออกแบบ real-time
3. **Beautiful Modal Notification**: แจ้งเตือนด้วย modal สวยงามแทน alert
4. **Auto Redirect**: นำทางไปหน้า login อัตโนมัติพร้อม countdown
5. **API Error Handling**: จัดการ 401 errors ในทุก API calls

### 🔄 **การทำงานแบบ Real-time:**

```
👤 User A ล็อกอินจาก Computer    → Session A ✅ Active
📱 User A ล็อกอินจาก Phone       → Session B ✅ Active, Session A ❌ Invalidated
💻 Computer (ภายใน 30 วินาที)     → 🚨 Modal แจ้งเตือน "ถูกออกจากระบบ"
⏰ Countdown 10 วินาที            → 🔄 Auto redirect ไป /login
📱 Phone ใช้งานได้ปกติ            → ✅ Session B ยังคงใช้งานได้
```

### 🎯 **ไม่ต้องกด Refresh อีกต่อไป!**

- ✅ **Session monitoring ทุก 30 วินาที**
- ✅ **Modal notification สวยงาม**
- ✅ **Auto redirect พร้อม countdown**
- ✅ **Error handling ในทุก API calls**
- ✅ **Real-time detection ไม่ต้องรอ refresh**
