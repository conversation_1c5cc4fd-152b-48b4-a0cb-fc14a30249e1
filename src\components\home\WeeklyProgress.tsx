//src\components\WeeklyProgress.tsx
"use client";

import { useState, useEffect } from "react";
import { TrendingUp, CheckCircle } from "lucide-react";

interface WeeklyProgressProps {
  selectedDate?: string;
  onRefresh?: boolean;
}

export default function WeeklyProgress({ selectedDate, onRefresh }: WeeklyProgressProps) {
  const [weeklyData, setWeeklyData] = useState<boolean[]>([false, false, false, false, false, false, false]);
  const [dayNames, setDayNames] = useState<string[]>(['จ', 'อ', 'พ', 'พฤ', 'ศ', 'ส', 'อา']);
  const [loading, setLoading] = useState(false);

  // สร้างอาร์เรย์ของ 7 วันในสัปดาห์นี้ (จันทร์-อาทิตย์)
  const generateWeekDays = () => {
    const days = [];
    const dayNames = [];
    const today = new Date();
    const thaiDayNames = ['อา', 'จ', 'อ', 'พ', 'พฤ', 'ศ', 'ส'];
    
    // หาวันจันทร์ของสัปดาห์นี้
    const dayOfWeek = today.getDay(); // 0=อาทิตย์, 1=จันทร์, ... 6=เสาร์
    const daysFromMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // ถ้าวันนี้เป็นอาทิตย์ ให้ถอยหลัง 6 วัน
    
    const monday = new Date(today);
    monday.setDate(today.getDate() - daysFromMonday);
    
    // สร้าง 7 วันจากจันทร์ถึงอาทิตย์
    for (let i = 0; i < 7; i++) {
      const date = new Date(monday);
      date.setDate(monday.getDate() + i);
      days.push(date.toISOString().split('T')[0]);
      dayNames.push(thaiDayNames[date.getDay()]);
    }
    
    return { days, dayNames };
  };

  // ดึงข้อมูลการบันทึกอาหารแต่ละวัน
  const fetchWeeklyData = async () => {
    setLoading(true);
    try {
      const { days, dayNames: correctDayNames } = generateWeekDays();
      setDayNames(correctDayNames);
      
      const weeklyStatus = await Promise.all(
        days.map(async (date) => {
          try {
            const res = await fetch(`/api/food-logs?date=${date}`);
            const data = await res.json();
            return data.foodLogs && data.foodLogs.length > 0;
          } catch (error) {
            return false;
          }
        })
      );
      
      setWeeklyData(weeklyStatus);
    } catch (error) {
      console.error('Error fetching weekly data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWeeklyData();
  }, [selectedDate, onRefresh]);

  // คำนวณความคืบหน้า
  const completedDays = weeklyData.filter(hasRecord => hasRecord).length;
  const progressPercentage = (completedDays / 7) * 100;

  return (
    <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-6 border border-white/50 shadow-lg">
      <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
        <TrendingUp className="w-5 h-5 text-emerald-600" />
        ความคืบหน้าสัปดาห์นี้
      </h3>
      
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">บันทึกครบทุกวัน</span>
          <div className="flex items-center gap-1">
            <span className="text-sm font-bold text-emerald-600">{completedDays}/7</span>
            <span className="text-xs text-gray-500">วัน</span>
          </div>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-emerald-400 to-teal-500 h-2 rounded-full transition-all duration-500" 
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
        
        <div className="grid grid-cols-7 gap-1 mt-4">
          {dayNames.map((day, index) => (
            <div key={`${day}-${index}`} className="text-center">
              <div className="text-xs text-gray-500 mb-1">{day}</div>
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                weeklyData[index] ? 'bg-emerald-100 text-emerald-600' : 'bg-gray-100 text-gray-400'
              }`}>
                {weeklyData[index] ? <CheckCircle className="w-4 h-4" /> : <span className="w-2 h-2 bg-gray-300 rounded-full" />}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}