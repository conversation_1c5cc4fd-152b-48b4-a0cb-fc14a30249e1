// AdminSidebar.tsx
"use client";

import { 
  Users, 
  ClipboardCheck, 
  Database, 
  BarChart3, 
  Settings, 
  FileText, 
  LogOut 
} from "lucide-react";
import React from 'react';
import { useRouter } from 'next/navigation';
import LogoutConfirmModal from '../LogoutConfirmModal';

interface AdminSidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  isSidebarOpen: boolean;
  onCloseSidebar: () => void;
}

export default function AdminSidebar({ 
  activeTab, 
  onTabChange, 
  isSidebarOpen, 
  onCloseSidebar 
}: AdminSidebarProps) {
  const router = useRouter();
  const [logoutModal, setLogoutModal] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const menuItems = [
    { id: 'overview', label: 'ภาพรวม', icon: BarChart3 },
    { id: 'users', label: 'จัดการผู้ใช้', icon: Users },
    { id: 'verifications', label: 'ตรวจสอบข้อมูล', icon: ClipboardCheck },
    { id: 'database', label: 'ฐานข้อมูลอาหาร', icon: Database },
    { id: 'ffq-questions', label: 'จัดการ FFQ', icon: FileText },
    { id: 'reports', label: 'รายงาน', icon: FileText },
    { id: 'settings', label: 'การตั้งค่า', icon: Settings }
  ];

  const handleTabClick = (tabId: string) => {
    onTabChange(tabId);
    onCloseSidebar();
  };

  const handleLogout = async () => {
    setIsLoading(true);
    try {
      // Call backend API to destroy session
      await fetch('/api/login', { method: 'DELETE' });
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      setTimeout(() => {
        setIsLoading(false);
        setLogoutModal(false);
        router.push('/login');
      }, 500);
    } catch (e) {
      setIsLoading(false);
      setLogoutModal(false);
      router.push('/login');
    }
  };

  return (
    <>
      {/* Overlay for mobile */}
      {isSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-20 lg:hidden" 
          onClick={onCloseSidebar}
        />
      )}

      {/* Sidebar */}
      <aside className={`
        fixed top-16 left-0 bottom-0 z-30 w-64 
        bg-white/90 backdrop-blur-lg border-r border-gray-200/50 
        shadow-xl transform transition-transform duration-300 ease-in-out
        ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0
      `}>
        <div className="flex flex-col h-full">
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {menuItems.map((item) => {
              const Icon = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => handleTabClick(item.id)}
                  className={`w-full flex items-center gap-3 px-4 py-3 text-left rounded-xl transition-all duration-200 ${
                    activeTab === item.id
                      ? 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg'
                      : 'text-gray-700 hover:bg-gray-100/80'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium">{item.label}</span>
                </button>
              );
            })}
          </nav>
          {/* Logout Button at Bottom */}
          <div className="px-4 py-4 border-t border-gray-200/50 bg-white/80">
            <button
              onClick={() => setLogoutModal(true)}
              className="w-full flex items-center gap-3 px-4 py-3 text-red-600 hover:bg-red-50 rounded-xl transition-all duration-200"
            >
              <LogOut className="w-5 h-5" />
              <span className="font-medium">ออกจากระบบ</span>
            </button>
          </div>
        </div>
      </aside>
      {/* Logout Modal: Render outside sidebar for center screen */}
      <LogoutConfirmModal
        isOpen={logoutModal}
        onClose={() => setLogoutModal(false)}
        onConfirm={handleLogout}
        userName={"Admin"}
        isLoading={isLoading}
      />
    </>
  );
}



