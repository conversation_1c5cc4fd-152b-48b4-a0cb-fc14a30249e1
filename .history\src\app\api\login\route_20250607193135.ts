
// src/app/api/login/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';
import { createRateLimiter, createRateLimitResponse, addRateLimitHeaders } from '@/lib/rate-limit';
import { createSession, destroySession, getSession, type SessionUser } from '@/lib/session';

const prisma = new PrismaClient();

interface LoginRequest {
  email: string;
  password: string;
  showPassword?: boolean; // For demo purposes
}

// Demo credentials for testing
const DEMO_CREDENTIALS = {
  email: process.env.DEMO_EMAIL || '<EMAIL>',
  password: process.env.DEMO_PASSWORD || 'demo123456'
};

function calculateAge(birthDate: Date): number {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
}

export async function POST(req: NextRequest) {
  try {
    console.log('🔐 Login API called');

    // Rate limiting check
    const loginLimiter = createRateLimiter('login');
    const rateLimitResult = loginLimiter(req);

    if (!rateLimitResult.success) {
      console.log('🚫 Rate limit exceeded for login');
      return createRateLimitResponse(rateLimitResult);
    }

    const body: LoginRequest = await req.json();
    console.log('📝 Login attempt for:', body.email);

    const { email, password, showPassword = false } = body;

    // Input validation
    if (!email || !password) {
      return NextResponse.json({
        error: 'กรอกชื่อผู้ใช้ และ รหัสผ่าน'
      }, { status: 400 });
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        error: 'รูปแบบอีเมลไม่ถูกต้อง'
      }, { status: 400 });
    }

    // Check for demo credentials first
    if (email === DEMO_CREDENTIALS.email && password === DEMO_CREDENTIALS.password) {
      console.log('🎭 Demo login successful');

      // Create demo user session
      const demoUser: SessionUser = {
        id: 999,
        email: DEMO_CREDENTIALS.email,
        fullName: 'Demo User',
        gender: 'male',
        height: 175,
        weight: 70,
        activityLevel: 'moderate',
        goal: 'maintain',
        foodAllergies: 'ไม่มี',
        bmr: 1650,
        tdee: 2558,
        role: 'user',
        bmi: 22.9,
        age: 30,
        loginTime: Date.now(),
        lastActivity: Date.now()
      };

      const response = NextResponse.json({
        message: 'เข้าสู่ระบบสำเร็จ! (Demo Account) 🎭',
        user: demoUser,
        showPassword: showPassword ? DEMO_CREDENTIALS.password : undefined,
        sessionBased: true
      }, { status: 200 });

      // Create session
      await createSession(req, response, demoUser, req.headers.get('user-agent') || undefined);
      addRateLimitHeaders(response, rateLimitResult);

      return response;
    }

    // Find user by email in database
    const user = await prisma.user.findUnique({
      where: {
        email: email.toLowerCase()
      }
    });

    if (!user) {
      console.log('❌ User not found:', email);
      return NextResponse.json({
        error: 'อีเมลหรือรหัสผ่านไม่ถูกต้อง'
      }, { status: 401 });
    }

    // Check if user is active
    if (!user.isActive) {
      console.log('❌ User account is inactive:', email);
      return NextResponse.json({
        error: 'บัญชีผู้ใช้ถูกระงับ กรุณาติดต่อผู้ดูแลระบบ'
      }, { status: 403 });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      console.log('❌ Invalid password for user:', email);
      return NextResponse.json({
        error: 'อีเมลหรือรหัสผ่านไม่ถูกต้อง'
      }, { status: 401 });
    }

    // Calculate age and BMI
    const age = calculateAge(user.birthDate);
    const bmi = Math.round((user.weight / Math.pow(user.height / 100, 2)) * 10) / 10;

    // Create session user object
    const sessionUser: SessionUser = {
      id: user.id,
      email: user.email,
      fullName: user.fullName,
      gender: user.gender,
      height: user.height,
      weight: user.weight,
      activityLevel: user.activityLevel,
      goal: user.goal,
      foodAllergies: user.foodAllergies || undefined,
      bmr: user.bmr || undefined,
      tdee: user.tdee || undefined,
      role: user.role,
      bmi,
      age,
      loginTime: Date.now(),
      lastActivity: Date.now()
    };

    console.log('✅ Login successful for user:', email);

    const response = NextResponse.json({
      message: 'เข้าสู่ระบบสำเร็จ! 🎉',
      user: sessionUser,
      sessionBased: true,
      showPassword: showPassword ? '••••••••' : undefined // Don't show real password
    }, { status: 200 });

    // Create session
    await createSession(req, response, sessionUser, req.headers.get('user-agent') || undefined);
    addRateLimitHeaders(response, rateLimitResult);

    return response;

  } catch (error: any) {
    console.error('❌ Login error:', error.message);

    // Handle specific Prisma errors
    if (error.code === 'P2025') {
      return NextResponse.json({
        error: 'รหัสผ่านไม่ต้อง'
      }, { status: 401 });
    }

    return NextResponse.json({
      error: 'ข้อผิดพลาดในระบบ ลองใหม่ครั้ง',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });

  } finally {
    await prisma.$disconnect();
  }
}

// Logout endpoint
export async function DELETE(req: NextRequest) {
  try {
    console.log('🚪 Logout API called');

    const response = NextResponse.json({
      message: 'ออกจากระบบสำเร็จ'
    }, { status: 200 });

    // Clear refresh token cookie
    response.cookies.set('refreshToken', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/'
    });

    // Clear remember me cookie
    response.cookies.set('rememberMe', '', {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/'
    });

    console.log('✅ All cookies cleared for logout');
    return response;

  } catch (error: any) {
    console.error('❌ Logout error:', error.message);
    return NextResponse.json({
      error: 'ข้อผิดพลาดในการออกจากระบบ'
    }, { status: 500 });
  }
}
