/*
  Warnings:

  - Added the required column `ffqSetId` to the `FFQQuestion` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "FFQQuestion" ADD COLUMN     "ffqSetId" INTEGER NOT NULL;

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "resetToken" TEXT,
ADD COLUMN     "resetTokenExpiry" TIMESTAMP(3);

-- CreateTable
CREATE TABLE "FFQSet" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "FFQSet_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "FFQQuestion" ADD CONSTRAINT "FFQQuestion_ffqSetId_fkey" FOREIGN KEY ("ffqSetId") REFERENCES "FFQSet"("id") ON DELETE CASCADE ON UPDATE CASCADE;
