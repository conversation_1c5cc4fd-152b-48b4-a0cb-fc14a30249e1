//src\components\form\DeleteConfirmModal.tsx
"use client";

import { useState } from "react";
import { X, Trash2, AlertTriangle, Clock, Utensils } from "lucide-react";

interface MealData {
  id: number;
  type: string;
  time: string;
  foods: string[];
  calories: number;
  status: string;
  beforeImage: string | null;
  afterImage: string | null;
}

interface DeleteConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  mealData: MealData | null;
  isDeleting?: boolean;
}

export default function DeleteConfirmModal({ 
  isOpen, 
  onClose, 
  onConfirm, 
  mealData,
  isDeleting = false 
}: DeleteConfirmModalProps) {
  
  if (!isOpen || !mealData) return null;

  const mealTypeLabels: Record<string, { label: string; color: string; bgColor: string }> = {
    breakfast: { label: "มื้อเช้า", color: "from-orange-400 to-amber-500", bgColor: "bg-orange-50" },
    lunch: { label: "มื้อกลางวัน", color: "from-yellow-400 to-orange-500", bgColor: "bg-yellow-50" },
    dinner: { label: "มื้อเย็น", color: "from-purple-400 to-pink-500", bgColor: "bg-purple-50" },
    snack: { label: "ของว่าง", color: "from-blue-400 to-cyan-500", bgColor: "bg-blue-50" }
  };

  const mealTypeData = mealTypeLabels[mealData.type] || mealTypeLabels.breakfast;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl w-full max-w-md shadow-2xl overflow-hidden">
        
        {/* Header */}
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900">ยืนยันการลบ</h3>
                <p className="text-sm text-gray-600">การดำเนินการนี้ไม่สามารถย้อนกลับได้</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              disabled={isDeleting}
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="mb-6">
            <p className="text-gray-700 mb-4">
              คุณต้องการลบรายการอาหารนี้ใช่หรือไม่?
            </p>
            
            {/* Meal Preview */}
            <div className="bg-gray-50 rounded-xl p-4 border border-gray-200">
              <div className="flex items-center gap-3 mb-3">
                <div className={`w-8 h-8 bg-gradient-to-r ${mealTypeData.color} rounded-lg flex items-center justify-center`}>
                  <Utensils className="w-4 h-4 text-white" />
                </div>
                <div>
                  <div className="font-semibold text-gray-900">{mealTypeData.label}</div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Clock className="w-3 h-3" />
                    <span>{mealData.time}</span>
                  </div>
                </div>
              </div>
              
              {/* Food Items */}
              <div className="mb-3">
                <div className="text-xs text-gray-500 mb-2">รายการอาหาร:</div>
                <div className="flex flex-wrap gap-1">
                  {mealData.foods.slice(0, 3).map((food, index) => (
                    <span 
                      key={index} 
                      className="px-2 py-1 bg-white text-gray-700 rounded-full text-xs border border-gray-200"
                    >
                      {food}
                    </span>
                  ))}
                  {mealData.foods.length > 3 && (
                    <span className="px-2 py-1 bg-gray-200 text-gray-600 rounded-full text-xs">
                      +{mealData.foods.length - 3} รายการ
                    </span>
                  )}
                </div>
              </div>
              
              {/* Calories */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">แคลอรี่:</span>
                <span className="font-bold text-amber-600">{mealData.calories} kcal</span>
              </div>
            </div>
          </div>

          {/* Warning Message */}
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-red-800 mb-1">คำเตือน</div>
                <div className="text-sm text-red-700">
                  ข้อมูลที่ลบแล้วจะไม่สามารถกู้คืนได้ รวมถึงรูปภาพและข้อมูลโภชนาการทั้งหมด
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <button
              type="button"
              onClick={onClose}
              disabled={isDeleting}
              className="flex-1 py-3 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              ยกเลิก
            </button>
            <button
              onClick={onConfirm}
              disabled={isDeleting}
              className="flex-1 py-3 px-4 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transition-all shadow-md hover:shadow-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {isDeleting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  <span>กำลังลบ...</span>
                </>
              ) : (
                <>
                  <Trash2 className="w-4 h-4" />
                  <span>ลบข้อมูล</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}