// C:\SofterM Github\Project-nutrition\src\app\admin\page.tsx
'use client';
import React, { useState } from 'react';
import AdminHeader from '../../components/admin/AdminHeader';
import AdminSidebar from '../../components/admin/AdminSidebar';
import AdminOverview from '../../components/admin/AdminOverview';
import AdminUsers from '../../components/admin/AdminUsers';
import AdminVerifications from '../../components/admin/AdminVerifications';
import AdminDatabase from '../../components/admin/AdminDatabase';
import AdminFFQQuestions from '../../components/admin/AdminFFQQuestions';
// Components สำหรับ Reports และ Settings (สร้างแบบง่าย)
const AdminReports = () => (
  <div className="space-y-6">
    <div className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 border border-white/50 shadow-lg">
      <h3 className="text-lg font-bold text-gray-800 mb-6">สร้างรายงาน</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-xl p-6 border border-emerald-200">
          <h4 className="font-bold text-emerald-800 mb-2">รายงานรายบุคคล</h4>
          <p className="text-sm text-emerald-700 mb-4">สร้างรายงานโภชนาการของผู้ใช้แต่ละคน</p>
          <button className="w-full px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors">
            สร้างรายงาน
          </button>
        </div>
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
          <h4 className="font-bold text-blue-800 mb-2">รายงานกลุ่ม</h4>
          <p className="text-sm text-blue-700 mb-4">วิเคราะห์ภาพรวมของกลุ่มผู้ใช้</p>
          <button className="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
            สร้างรายงาน
          </button>
        </div>
        <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-200">
          <h4 className="font-bold text-purple-800 mb-2">ส่งออกข้อมูล</h4>
          <p className="text-sm text-purple-700 mb-4">ส่งออกข้อมูลในรูปแบบ Excel/CSV</p>
          <button className="w-full px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
            ส่งออก
          </button>
        </div>
      </div>
    </div>
  </div>
);

const AdminSettings = () => (
  <div className="space-y-6">
    <div className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 border border-white/50 shadow-lg">
      <h3 className="text-lg font-bold text-gray-800 mb-6">การตั้งค่าระบบ</h3>
      <div className="space-y-6">
        <div className="border-b border-gray-200 pb-6">
          <h4 className="font-semibold text-gray-800 mb-4">การตั้งค่าทั่วไป</h4>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-gray-900">อนุมัติผู้ใช้ใหม่อัตโนมัติ</div>
                <div className="text-sm text-gray-500">ผู้ใช้ใหม่จะสามารถเข้าใช้งานได้ทันที</div>
              </div>
              <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-emerald-500 transition-colors">
                <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
              </button>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-gray-900">ส่งการแจ้งเตือนทางอีเมล</div>
                <div className="text-sm text-gray-500">แจ้งเตือนเมื่อมีข้อมูลใหม่ที่ต้องตรวจสอบ</div>
              </div>
              <button className="relative inline-flex h-6 w-11 items-center rounded-full bg-emerald-500 transition-colors">
                <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
              </button>
            </div>
          </div>
        </div>
        
        <div>
          <h4 className="font-semibold text-gray-800 mb-4">การตั้งค่าโภชนาการ</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ค่าแคลอรี่เริ่มต้น (kcal/วัน)
              </label>
              <input
                type="number"
                defaultValue="2000"
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:border-emerald-400 focus:ring-2 focus:ring-emerald-100"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                จำนวนมื้อต่อวันที่แนะนำ
              </label>
              <input
                type="number"
                defaultValue="3"
                className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:border-emerald-400 focus:ring-2 focus:ring-emerald-100"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState('overview');
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return <AdminOverview />;
      case 'users':
        return <AdminUsers />;
      case 'verifications':
        return <AdminVerifications />;
      case 'database':
        return <AdminDatabase />;
      case 'ffq-questions':
        return <AdminFFQQuestions />;
      case 'reports':
        return <AdminReports />;
      case 'settings':
        return <AdminSettings />;
      default:
        return <AdminOverview />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Fixed Header */}
      <AdminHeader 
        isSidebarOpen={isSidebarOpen}
        onToggleSidebar={toggleSidebar}
      />

      {/* Fixed Sidebar */}
      <AdminSidebar
        activeTab={activeTab}
        onTabChange={setActiveTab}
        isSidebarOpen={isSidebarOpen}
        onCloseSidebar={closeSidebar}
      />

      {/* Main Content - ชิดบนชิดซ้าย */}
      <main className={`transition-all duration-300 ml-64 pt-2`}>
        <div className="p-4">
          {renderContent()}
        </div>
      </main>
    </div>
  );
}