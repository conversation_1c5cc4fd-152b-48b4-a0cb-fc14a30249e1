import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

// GET a single FFQ Set by ID
export async function GET(request: Request, context: { params: Promise<{ setId: string }> }) {
  const { setId } = await context.params;
  try {
    const set = await prisma.fFQSet.findUnique({
      where: { id: parseInt(setId, 10) },
      include: { questions: true },
    });

    if (!set) {
      return NextResponse.json({ error: 'FFQ Set not found' }, { status: 404 });
    }

    return NextResponse.json(set);
  } catch (error) {
    console.error(`Error fetching FFQ set ${setId}:`, error);
    return NextResponse.json({ error: 'Failed to fetch FFQ set' }, { status: 500 });
  }
}

// PUT (update) a single FFQ Set by ID
export async function PUT(request: Request, context: { params: Promise<{ setId: string }> }) {
  const { setId } = await context.params;
  try {
    const body = await request.json();
    const { name, description, isActive } = body;

    const updatedSet = await prisma.fFQSet.update({
      where: { id: parseInt(setId, 10) },
      data: {
        name,
        description,
        isActive,
      },
    });

    return NextResponse.json(updatedSet);
  } catch (error) {
    console.error(`Error updating FFQ set ${setId}:`, error);
    return NextResponse.json({ error: 'Failed to update FFQ set' }, { status: 500 });
  }
}

// DELETE a single FFQ Set by ID
export async function DELETE(request: Request, context: { params: Promise<{ setId: string }> }) {
  const { setId } = await context.params;
  try {
    // Optional: Add logic to handle related questions, e.g., delete them or un-link them.
    // For now, we will delete the set and Prisma will handle relations based on schema.
    await prisma.fFQSet.delete({
      where: { id: parseInt(setId, 10) },
    });

    return new NextResponse(null, { status: 204 }); // No Content
  } catch (error) {
    console.error(`Error deleting FFQ set ${setId}:`, error);
    return NextResponse.json({ error: 'Failed to delete FFQ set' }, { status: 500 });
  }
}