"use client";

import { useState, useEffect } from "react";
import { storeUserData, clearStoredUser, isLoggedIn } from "@/lib/auth-utils";
import { 
  Mail, Lock, Eye, EyeOff, ArrowRight, 
  Utensils, Shield, Heart, User, CheckCircle, 
  AlertCircle, Sparkles, Coffee, TrendingUp
} from "lucide-react";

export default function ModernLoginPage() {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    rememberMe: false
  });

  const [showPassword, setShowPassword] = useState(false);
  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [showAutofillManager, setShowAutofillManager] = useState(false);
  const [savedCredentials, setSavedCredentials] = useState([]);
  const [focusedField, setFocusedField] = useState("");

  // Check if user is already logged in and load saved credentials
  useEffect(() => {
    const initializePage = async () => {
      try {
        // Check session
        const sessionResponse = await fetch('/api/session', {
          method: 'GET',
          credentials: 'include'
        });

        if (sessionResponse.ok) {
          // User is already logged in, redirect to home
          window.location.href = "/";
          return;
        }

        // Load saved credentials for autofill
        const autofillResponse = await fetch('/api/autofill', {
          method: 'GET',
          credentials: 'include'
        });

        if (autofillResponse.ok) {
          const data = await autofillResponse.json();

          // Auto-fill with the most recent credential if available
          if (data.hasCredential && data.credential) {
            const credential = data.credential;
            setFormData({
              email: credential.email,
              password: credential.password,
              rememberMe: true
            });
            console.log('✅ Auto-filled with saved credentials:', credential.email);
          }
        }
      } catch (error) {
        console.log('Initialization error:', error);
      }
    };

    initializePage();
  }, []);

  const handleChange = (e: { target: { name: any; value: any; type: any; checked: any; }; }) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSubmit = async (e: { preventDefault: () => void; }) => {
    e.preventDefault();

    // ป้อง<|im_start|>การกดซ้ำ
    if (loading) {
      return;
    }

    setMessage("");
    setLoading(true);

    try {
      const res = await fetch("/api/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: 'include', // Include cookies for session
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
          rememberMe: formData.rememberMe // For autofill
        }),
      });

      const data = await res.json();

      if (res.ok) {
        setMessage(data.message || "เข้าสู่ระบบสำเร็จ! กำลังนำทางไปหน้าหลัก...");

        // Session-based authentication with autofill
        console.log('✅ Session-based login successful');
        if (data.autofillEnabled) {
          console.log('✅ Credentials saved for autofill');
        }

        // Store user data ใน sessionStorage เพื่อ<|im_start|>ความเร็ว
        if (data.user) {
          sessionStorage.setItem('tempUserData', JSON.stringify({
            name: data.user.fullName || "ผู้ใช้งาน",
            age: data.user.age || 30,
            weight: data.user.weight || 70,
            height: data.user.height || 175,
            bmi: data.user.bmi || 22.9,
            goal: data.user.goal || "health",
            dailyCalories: data.user.tdee || 2200,
            consumedCalories: 1130,
            remainingCalories: (data.user.tdee || 2200) - 1130
          }));
        }

        // Redirect to home page เร็ว<|im_start|>้น (ไม่ reset loading เพื่อป้อง<|im_start|>การกดซ้ำ)
        setTimeout(() => {
          window.location.href = "/";
        }, 800); // ลดเวลาจาก 1500 → 800ms
      } else {
        setMessage(data.error || "อีเมลหรือรหัสผ่านไม่ถูกต้อง");
        setLoading(false); // Reset loading เฉพาะเมื่อ error
      }

    } catch (error) {
      setMessage("เกิดข้อผิดพลาดในการเชื่อมต่อ กรุณาลองใหม่อีกครั้ง");
      setLoading(false); // Reset loading เฉพาะเมื่อ error
    }
  };

  // ฟังก์ชันจัดการ autofill
  const loadSavedCredentials = async () => {
    try {
      const response = await fetch('/api/autofill', {
        method: 'POST',
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        setSavedCredentials(data.credentials || []);
      }
    } catch (error) {
      console.error('Error loading saved credentials:', error);
    }
  };

  const clearAllAutofill = async () => {
    try {
      const response = await fetch('/api/autofill', {
        method: 'DELETE',
        credentials: 'include'
      });

      if (response.ok) {
        setSavedCredentials([]);
        setMessage('ลบข้อมูล autofill ทั้งหมดแล้ว');
        setShowAutofillManager(false);
      }
    } catch (error) {
      setMessage('เกิดข้อผิดพลาดในการลบข้อมูล autofill');
    }
  };

  const toggleAutofillManager = () => {
    if (!showAutofillManager) {
      loadSavedCredentials();
    }
    setShowAutofillManager(!showAutofillManager);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center py-8 px-4 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 rounded-full bg-gradient-to-br from-blue-200/30 to-indigo-300/30 blur-3xl" />
        <div className="absolute top-1/2 -left-40 w-96 h-96 rounded-full bg-gradient-to-br from-emerald-200/30 to-teal-300/30 blur-3xl" />
        <div className="absolute -bottom-40 right-1/3 w-80 h-80 rounded-full bg-gradient-to-br from-purple-200/30 to-pink-300/30 blur-3xl" />
      </div>
      
      <div className="relative z-10 w-full max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        
        {/* Left Side - Welcome Content */}
        <div className="hidden lg:block space-y-8">
          <div className="text-center lg:text-left">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-3xl mb-8 shadow-2xl">
              <Utensils className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-emerald-600 via-teal-600 to-blue-600 bg-clip-text text-transparent mb-6">
              ยินดีต้อนรับกลับ!
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              เข้าสู่ระบบเพื่อติดตามการทานอาหารและ<br />
              วิเคราะห์โภชนาการของคุณต่อ
            </p>
          </div>
          
          {/* Feature Highlights */}
          <div className="space-y-6">
            <div className="flex items-center gap-4 p-4 bg-white/60 backdrop-blur-lg rounded-2xl border border-white/40">
              <div className="w-12 h-12 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-xl flex items-center justify-center shadow-md">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-800">ติดตามความคืบหน้า</h3>
                <p className="text-sm text-gray-600">ดูสถิติและการพัฒนาของคุณ</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 p-4 bg-white/60 backdrop-blur-lg rounded-2xl border border-white/40">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-xl flex items-center justify-center shadow-md">
                <Coffee className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-800">บันทึกง่ายๆ ทุกมื้อ</h3>
                <p className="text-sm text-gray-600">จดบันทึกอาหารและเครื่องดื่ม</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 p-4 bg-white/60 backdrop-blur-lg rounded-2xl border border-white/40">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-xl flex items-center justify-center shadow-md">
                <Heart className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-800">คำแนะนำส่วนตัว</h3>
                <p className="text-sm text-gray-600">รับคำแนะนำเฉพาะบุคคล</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Right Side - Login Form */}
        <div className="w-full max-w-md mx-auto lg:mx-0">
          <div className="bg-white/70 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/50 overflow-hidden">
            <div className="p-8">
              
              {/* Mobile Header */}
              <div className="text-center mb-8 lg:hidden">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl mb-4 shadow-lg">
                  <Utensils className="w-8 h-8 text-white" />
                </div>
                <h2 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  เข้าสู่ระบบ
                </h2>
              </div>
              
              {/* Desktop Header */}
              <div className="hidden lg:block text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-800 mb-2">เข้าสู่ระบบ</h2>
                <p className="text-gray-600">กรอกข้อมูลเพื่อเข้าใช้งาน</p>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Email Field */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    อีเมล <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <Mail className={`absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 transition-colors ${
                      focusedField === 'email' ? 'text-emerald-500' : 'text-gray-400'
                    }`} />
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      onFocus={() => setFocusedField('email')}
                      onBlur={() => setFocusedField('')}
                      className={`pl-12 pr-4 w-full py-4 border-2 rounded-2xl transition-all duration-300 text-black placeholder-gray-400 ${
                        focusedField === 'email' 
                          ? 'border-emerald-400 ring-4 ring-emerald-100 bg-white' 
                          : 'border-gray-200 bg-gray-50/50 hover:border-gray-300'
                      }`}
                      placeholder="กรอกอีเมลของคุณ"
                      required
                    />
                    {formData.email && (
                      <CheckCircle className="absolute right-4 top-1/2 -translate-y-1/2 w-5 h-5 text-emerald-500" />
                    )}
                  </div>
                </div>
                
                {/* Password Field */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    รหัสผ่าน <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <Lock className={`absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 transition-colors ${
                      focusedField === 'password' ? 'text-emerald-500' : 'text-gray-400'
                    }`} />
                    <input
                      type={showPassword ? 'text' : 'password'}
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      onFocus={() => setFocusedField('password')}
                      onBlur={() => setFocusedField('')}
                      className={`pl-12 pr-12 w-full py-4 border-2 rounded-2xl transition-all duration-300 text-black placeholder-gray-400 ${
                        focusedField === 'password' 
                          ? 'border-emerald-400 ring-4 ring-emerald-100 bg-white' 
                          : 'border-gray-200 bg-gray-50/50 hover:border-gray-300'
                      }`}
                      placeholder="กรอกรหัสผ่าน"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                </div>
                
                {/* Remember Me & Forgot Password */}
                <div className="flex items-center justify-between">
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      name="rememberMe"
                      checked={formData.rememberMe}
                      onChange={handleChange}
                      className="w-4 h-4 text-emerald-600 border-gray-300 rounded focus:ring-emerald-500 transition-colors"
                    />
                    <span className="ml-2 text-sm text-gray-600">จดจำการเข้าสู่ระบบ</span>
                  </label>
                  <button
                    type="button"
                    className="text-sm text-emerald-600 hover:text-emerald-700 font-medium underline decoration-emerald-300 hover:decoration-emerald-500 transition-colors"
                  >
                    ลืมรหัสผ่าน?
                  </button>
                </div>
                
                {/* Login Button */}
                <button
                  type="submit"
                  disabled={!formData.email || !formData.password || loading}
                  className="w-full py-4 bg-gradient-to-r from-emerald-500 to-teal-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center"
                >
                  {loading ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-3"></div>
                      กำลังเข้าสู่ระบบ...
                    </>
                  ) : (
                    <>
                      เข้าสู่ระบบ
                      <ArrowRight className="ml-3 w-5 h-5" />
                    </>
                  )}
                </button>
                
                {/* Success/Error Message */}
                {message && (
                  <div className={`p-4 rounded-2xl flex items-center gap-3 transition-all duration-500 ${
                    message.includes("สำเร็จ") 
                      ? 'bg-emerald-50 border-2 border-emerald-200 text-emerald-700' 
                      : 'bg-red-50 border-2 border-red-200 text-red-700'
                  }`}>
                    {message.includes("สำเร็จ") ? (
                      <div className="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-white" />
                      </div>
                    ) : (
                      <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                        <AlertCircle className="w-4 h-4 text-white" />
                      </div>
                    )}
                    <span className="font-medium">{message}</span>
                  </div>
                )}

                {/* Autofill Management */}
                <div className="mt-6 text-center">
                  <button
                    type="button"
                    onClick={toggleAutofillManager}
                    className="text-sm text-emerald-600 hover:text-emerald-700 underline decoration-emerald-300 hover:decoration-emerald-500 transition-colors"
                  >
                    จัดการข้อมูล Autofill
                  </button>
                </div>

                {showAutofillManager && (
                  <div className="mt-4 p-4 bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl border border-gray-200">
                    <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                      <Shield className="w-4 h-4" />
                      ข้อมูล Autofill ที่บันทึกไว้
                    </h3>

                    {savedCredentials.length > 0 ? (
                      <div className="space-y-3">
                        {savedCredentials.map((cred: any, index: number) => (
                          <div key={index} className="flex justify-between items-center p-3 bg-white rounded-xl border border-gray-100 shadow-sm">
                            <div className="text-sm">
                              <div className="font-medium text-gray-800">{cred.displayName}</div>
                              <div className="text-gray-500">{cred.email}</div>
                            </div>
                            <div className="text-xs text-gray-400">
                              {new Date(cred.savedAt).toLocaleDateString('th-TH')}
                            </div>
                          </div>
                        ))}

                        <button
                          onClick={clearAllAutofill}
                          className="w-full mt-3 py-3 px-4 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl hover:from-red-600 hover:to-red-700 text-sm font-medium transition-all duration-200 flex items-center justify-center gap-2"
                        >
                          <AlertCircle className="w-4 h-4" />
                          ลบข้อมูล Autofill ทั้งหมด
                        </button>
                      </div>
                    ) : (
                      <div className="text-center py-6">
                        <Shield className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                        <p className="text-sm text-gray-500">
                          ไม่มีข้อมูล Autofill ที่บันทึกไว้
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </form>
            </div>
            
            {/* Footer */}
            <div className="px-8 py-6 bg-gradient-to-r from-gray-50/80 to-blue-50/80 backdrop-blur border-t border-gray-100">
              <p className="text-center text-gray-600">
                ยังไม่มีบัญชี?{" "}
                <a
                  href="/register"
                  className="font-semibold text-emerald-600 hover:text-emerald-700 underline decoration-emerald-300 hover:decoration-emerald-500 transition-colors"
                >
                  สมัครสมาชิก
                </a>
              </p>
            </div>
          </div>
          
          {/* Mobile Feature Cards */}
          <div className="mt-8 lg:hidden">
            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-center gap-3 p-4 bg-white/60 backdrop-blur-lg rounded-2xl border border-white/40">
                <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-lg flex items-center justify-center">
                  <Shield className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 text-sm">ความปลอดภัยสูง</h3>
                  <p className="text-xs text-gray-600">ข้อมูลได้รับการปกป้อง</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-4 bg-white/60 backdrop-blur-lg rounded-2xl border border-white/40">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-lg flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 text-sm">ประสบการณ์ที่ดี</h3>
                  <p className="text-xs text-gray-600">ใช้งานง่าย เข้าใจง่าย</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}