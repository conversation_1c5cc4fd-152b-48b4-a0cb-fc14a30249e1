import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

// GET all FFQ Sets
export async function GET() {
  try {
    const sets = await prisma.fFQSet.findMany({
      include: {
        questions: true, // Include related questions
      },
    });
    return NextResponse.json(sets);
  } catch (error) {
    console.error('Error fetching FFQ sets:', error);
    return NextResponse.json({ error: 'Failed to fetch FFQ sets' }, { status: 500 });
  }
}

// POST a new FFQ Set
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { name, description, isActive } = body;

    if (!name) {
      return NextResponse.json({ error: 'Name is required' }, { status: 400 });
    }

    const newSet = await prisma.fFQSet.create({
      data: {
        name,
        description,
        isActive,
      },
    });
    return NextResponse.json(newSet, { status: 201 });
  } catch (error) {
    console.error('Error creating FFQ set:', error);
    return NextResponse.json({ error: 'Failed to create FFQ set' }, { status: 500 });
  }
}
