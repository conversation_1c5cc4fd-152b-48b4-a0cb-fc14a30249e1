//src\components\home\MealSection.tsx
"use client";

import { useEffect, useState } from "react";
import { 
  Plus, Clock, CheckCircle, AlertCircle, Edit3, Utensils, Calendar, Camera,
  Sunrise, Sun, Sunset, Coffee, Trash2, Zap
} from "lucide-react";
import AddMealModal from "../form/AddMealModal";
import EditMealModal from "../form/EditMealModal";
import DeleteConfirmModal from "../form/DeleteConfirmModal";

interface Meal {
  id: number;
  type: string;
  time: string;
  foods: string[];
  calories: number;
  status: string;
  beforeImage: string | null;
  afterImage: string | null;
}

// Interface สำหรับข้อมูลดิบจาก API
interface FoodLogItem {
  id: string;
  foodId: number;
  amount: number;
  unit: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  food: {
    id: string;
    name: string;
    category: string;
    standardUnit: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
}

interface MealData {
  id: string;
  mealType: string;
  date: string;
  mealTime: string;
  beforeImage: string | null;
  afterImage: string | null;
  notes: string | null;
  items: FoodLogItem[];
}

interface MealSectionProps {
  selectedDate: string;
  onDateChange: (date: string) => void;
}

export default function MealSection({ 
  selectedDate, 
  onDateChange, 
}: MealSectionProps) {
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [meals, setMeals] = useState<Meal[]>([]);
  const [rawMealData, setRawMealData] = useState<MealData[]>([]); // เก็บข้อมูลดิบจาก API
  const [selectedMealForEdit, setSelectedMealForEdit] = useState<MealData | null>(null);
  const [selectedMealForDelete, setSelectedMealForDelete] = useState<Meal | null>(null);
  const [loading, setLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [imageModal, setImageModal] = useState<{ url: string; alt: string } | null>(null);

  const mealTypes = [
    { 
      type: "breakfast", 
      label: "มื้อเช้า", 
      icon: Sunrise, 
      color: "from-orange-400 to-amber-500", 
      bgColor: "bg-orange-50",
      time: "06:00-10:00" 
    },
    { 
      type: "lunch", 
      label: "มื้อกลางวัน", 
      icon: Sun, 
      color: "from-yellow-400 to-orange-500", 
      bgColor: "bg-yellow-50",
      time: "11:00-14:00" 
    },
    { 
      type: "dinner", 
      label: "มื้อเย็น", 
      icon: Sunset, 
      color: "from-purple-400 to-pink-500", 
      bgColor: "bg-purple-50",
      time: "17:00-20:00" 
    },
    { 
      type: "snack", 
      label: "ของว่าง", 
      icon: Coffee, 
      color: "from-blue-400 to-cyan-500", 
      bgColor: "bg-blue-50",
      time: "ตลอดวัน" 
    }
  ];

  // Format date for display
  const formatDateDisplay = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "วันนี้";
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return "พรุ่งนี้";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "เมื่อวาน";
    } else {
      return date.toLocaleDateString('th-TH', { 
        weekday: 'long',
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });
    }
  };

  // Fetch meals from API
  useEffect(() => {
    async function fetchMeals() {
      setLoading(true);
      try {
        const res = await fetch(`/api/food-logs?date=${selectedDate}`);
        const data = await res.json();
        if (data.foodLogs) {
          // เก็บข้อมูลดิบไว้สำหรับ EditModal
          const rawData: MealData[] = data.foodLogs.map((log: any) => ({
            id: log.id.toString(),
            mealType: log.mealType,
            date: selectedDate,
            mealTime: log.mealTime,
            beforeImage: log.beforeImage || null,
            afterImage: log.afterImage || null,
            notes: log.notes || null,
            items: log.items
          }));
          setRawMealData(rawData);

          // แปลงเป็น format สำหรับแสดงผล
          setMeals(data.foodLogs.map((log: any) => ({
            id: log.id,
            type: log.mealType,
            time: log.mealTime ? new Date(log.mealTime).toLocaleTimeString('th-TH', { hour: '2-digit', minute: '2-digit' }) : '',
            foods: log.items.map((item: any) => `${item.food.name} ${item.amount} ${item.unit}`),
            calories: log.items.reduce((sum: number, item: any) => sum + item.calories, 0),
            status: log.isVerified ? 'verified' : 'pending',
            beforeImage: log.beforeImage || null,
            afterImage: log.afterImage || null,
          })));
        } else {
          setMeals([]);
          setRawMealData([]);
        }
      } catch {
        setMeals([]);
        setRawMealData([]);
      } finally {
        setLoading(false);
      }
    }
    fetchMeals();
  }, [selectedDate, showAddModal, showEditModal]); // เพิ่ม showEditModal เพื่อ refresh หลังแก้ไข

  const getTotalCalories = () => {
    return meals.reduce((total, meal) => total + meal.calories, 0);
  };
  const getCompletedMeals = () => {
    return meals.filter(meal => meal.status === 'verified').length;
  };

  const handleAddMealSubmit = (mealData: any) => {
    console.log('New meal data:', mealData);
    // Handle adding meal logic here
  };

  const handleEditMealSubmit = (mealData: any) => {
    console.log('Updated meal data:', mealData);
    // Handle updating meal logic here
  };

  // ฟังก์ชันเปิด EditModal
  const handleEditMeal = (mealId: number) => {
    const mealToEdit = rawMealData.find(meal => meal.id === mealId.toString());
    if (mealToEdit) {
      setSelectedMealForEdit(mealToEdit);
      setShowEditModal(true);
    }
  };

  // ฟังก์ชันปิด EditModal
  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setSelectedMealForEdit(null);
  };

  // ฟังก์ชันเปิด DeleteModal
  const handleDeleteMeal = (mealId: number) => {
    const mealToDelete = meals.find(meal => meal.id === mealId);
    if (mealToDelete) {
      setSelectedMealForDelete(mealToDelete);
      setShowDeleteModal(true);
    }
  };

  // ฟังก์ชันปิด DeleteModal
  const handleCloseDeleteModal = () => {
    setShowDeleteModal(false);
    setSelectedMealForDelete(null);
  };

  // ฟังก์ชันยืนยันการลบ (เรียก API จริง)
  const handleConfirmDelete = async () => {
    if (!selectedMealForDelete) return;
    
    setIsDeleting(true);
    try {
      const res = await fetch(`/api/food-logs/${selectedMealForDelete.id}`, {
        method: 'DELETE'
      });
      
      if (res.ok) {
        // Refresh data
        setMeals(meals.filter(meal => meal.id !== selectedMealForDelete.id));
        setRawMealData(rawMealData.filter(meal => meal.id !== selectedMealForDelete.id.toString()));
        handleCloseDeleteModal();
      } else {
        alert('ไม่สามารถลบได้ กรุณาลองใหม่');
      }
    } catch (error) {
      alert('เกิดข้อผิดพลาด กรุณาลองใหม่');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <>
      <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-6 border border-white/50 shadow-lg">
        
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Calendar className="w-6 h-6 text-emerald-600" />
            <div>
              <h2 className="text-xl font-bold text-gray-800">บันทึกอาหารวันนี้</h2>
              <p className="text-sm text-gray-600">{formatDateDisplay(selectedDate)}</p>
            </div>
          </div>
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => onDateChange(e.target.value)}
            className="px-4 py-2 border border-gray-200 rounded-xl focus:border-emerald-400 focus:ring-2 focus:ring-emerald-100 transition-colors text-gray-900"
            style={{ colorScheme: 'light' }}
          />
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="bg-emerald-50 rounded-2xl p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Zap className="w-5 h-5 text-emerald-600" />
            </div>
            <div className="text-2xl font-bold text-emerald-700">{getTotalCalories()}</div>
            <div className="text-xs text-emerald-600">แคลอรี่รวม</div>
          </div>
          <div className="bg-blue-50 rounded-2xl p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <CheckCircle className="w-5 h-5 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-blue-700">{getCompletedMeals()}</div>
            <div className="text-xs text-blue-600">มื้อยืนยันแล้ว</div>
          </div>
          <div className="bg-purple-50 rounded-2xl p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Utensils className="w-5 h-5 text-purple-600" />
            </div>
            <div className="text-2xl font-bold text-purple-700">{meals.length}</div>
            <div className="text-xs text-purple-600">รายการทั้งหมด</div>
          </div>
        </div>

        {/* Add Button */}
        <div className="mb-6">
          <button
            onClick={() => setShowAddModal(true)}
            className="w-full flex items-center justify-center gap-3 py-4 px-6 bg-gradient-to-r from-emerald-500 to-teal-600 text-white rounded-2xl hover:from-emerald-600 hover:to-teal-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] font-semibold"
          >
            <Plus className="w-6 h-6" />
            บันทึกอาหาร
          </button>
        </div>

        {/* Meals by Type */}
        <div className="space-y-6">
          {mealTypes.map((mealType) => {
            const Icon = mealType.icon;
            const mealsOfType = meals.filter(meal => meal.type === mealType.type);
            const mealCalories = mealsOfType.reduce((total, meal) => total + meal.calories, 0);
            
            return (
              <div key={mealType.type} className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden transition-all duration-300 hover:shadow-xl">
                {/* Meal Type Header */}
                <div className={`${mealType.bgColor} p-5`}> 
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                    <div className="flex items-center gap-4">
                      <div className={`w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r ${mealType.color} rounded-xl flex items-center justify-center shadow-lg`}>
                        <Icon className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-bold text-gray-800 text-base sm:text-lg">{mealType.label}</h3>
                        <div className="flex flex-wrap items-center gap-2 text-xs sm:text-sm text-gray-600">
                          <Clock className="w-4 h-4" />
                          <span>{mealType.time}</span>
                          {mealCalories > 0 && (
                            <>
                              <span className="text-gray-400">•</span>
                              <span className="font-medium text-emerald-600">{mealCalories} แคลอรี่</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    {mealsOfType.length > 0 && (
                      <div className="text-xs sm:text-sm text-gray-500 font-medium text-right">
                        {mealsOfType.length} รายการ
                      </div>
                    )}
                  </div>
                </div>
                {/* Meal Entries */}
                {mealsOfType.length > 0 ? (
                  <div className="p-5 space-y-6">
                    {mealsOfType.map((meal) => (
                      <div key={meal.id} className="border-t border-gray-100 pt-6 first:border-t-0 first:pt-0">
                        {/* Meal Header Info */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-4">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-2 text-xs sm:text-sm">
                              <Clock className="w-4 h-4 text-gray-400" />
                              <span className="font-semibold text-gray-800">{meal.time}</span>
                              {/* Action Buttons for mobile only */}
                              <span className="flex gap-1 sm:hidden ml-2">
                                <button 
                                  onClick={() => handleEditMeal(meal.id)}
                                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors" 
                                  title="แก้ไข"
                                >
                                  <Edit3 className="w-4 h-4 text-gray-400 hover:text-gray-600" />
                                </button>
                                <button 
                                  onClick={() => handleDeleteMeal(meal.id)}
                                  className="p-2 hover:bg-red-50 rounded-lg transition-colors" 
                                  title="ลบ"
                                >
                                  <Trash2 className="w-4 h-4 text-gray-400 hover:text-red-500" />
                                </button>
                              </span>
                            </div>
                            {meal.status === 'verified' ? (
                              <div className="flex items-center gap-1 px-2 sm:px-3 py-1 bg-emerald-100 text-emerald-700 rounded-full text-xs sm:text-sm font-medium">
                                <CheckCircle className="w-4 h-4" />
                                <span>ยืนยันแล้ว</span>
                              </div>
                            ) : (
                              <div className="flex items-center gap-1 px-2 sm:px-3 py-1 bg-amber-100 text-amber-700 rounded-full text-xs sm:text-sm font-medium">
                                <AlertCircle className="w-4 h-4" />
                                <span>รอยืนยัน</span>
                              </div>
                            )}
                          </div>
                          {/* Action Buttons for desktop */}
                          <div className="hidden sm:flex items-center gap-1">
                            <button 
                              onClick={() => handleEditMeal(meal.id)}
                              className="p-2 hover:bg-gray-100 rounded-lg transition-colors" 
                              title="แก้ไข"
                            >
                              <Edit3 className="w-4 h-4 text-gray-400 hover:text-gray-600" />
                            </button>
                            <button 
                              onClick={() => handleDeleteMeal(meal.id)}
                              className="p-2 hover:bg-red-50 rounded-lg transition-colors" 
                              title="ลบ"
                            >
                              <Trash2 className="w-4 h-4 text-gray-400 hover:text-red-500" />
                            </button>
                          </div>
                        </div>
                        {/* Images Section */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                          {/* Before Image */}
                          <div>
                            <div className="text-xs text-gray-500 mb-2 flex items-center gap-1">
                              <Camera className="w-3 h-3" /> รูปก่อนทาน
                            </div>
                            {meal.beforeImage ? (
                              <button type="button" onClick={() => setImageModal({ url: meal.beforeImage!, alt: 'รูปก่อนทาน' })} className="block relative group cursor-pointer w-full">
                                <div className="w-full h-28 sm:h-32 bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-xl overflow-hidden">
                                  <img 
                                    src={meal.beforeImage} 
                                    alt="รูปก่อนทาน"
                                    className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                                  />
                                </div>
                                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 rounded-xl transition-all duration-300 flex items-center justify-center">
                                  <div className="w-8 h-8 bg-white/90 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Camera className="w-4 h-4 text-gray-600" />
                                  </div>
                                </div>
                                <div className="text-xs text-gray-500 mt-1 text-center">{meal.time}</div>
                              </button>
                            ) : (
                              <div className="w-full h-28 sm:h-32 bg-gray-100 rounded-xl flex items-center justify-center border-2 border-dashed border-gray-300">
                                <div className="text-center">
                                  <Camera className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                                  <div className="text-xs text-gray-500">ยังไม่มีรูป</div>
                                </div>
                              </div>
                            )}
                          </div>
                          {/* After Image */}
                          <div>
                            <div className="text-xs text-gray-500 mb-2 flex items-center gap-1">
                              <Camera className="w-3 h-3" /> รูปหลังทาน
                            </div>
                            {meal.afterImage ? (
                              <button type="button" onClick={() => setImageModal({ url: meal.afterImage!, alt: 'รูปหลังทาน' })} className="block relative group cursor-pointer w-full">
                                <div className="w-full h-28 sm:h-32 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl overflow-hidden">
                                  <img 
                                    src={meal.afterImage} 
                                    alt="รูปหลังทาน"
                                    className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                                  />
                                </div>
                                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 rounded-xl transition-all duration-300 flex items-center justify-center">
                                  <div className="w-8 h-8 bg-white/90 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Camera className="w-4 h-4 text-gray-600" />
                                  </div>
                                </div>
                                <div className="text-xs text-gray-500 mt-1 text-center">{meal.time}</div>
                              </button>
                            ) : (
                              <div className="w-full h-28 sm:h-32 bg-gray-100 rounded-xl flex items-center justify-center border-2 border-dashed border-gray-300">
                                <div className="text-center">
                                  <Camera className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                                  <div className="text-xs text-gray-500">ยังไม่มีรูป</div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                        {/* Food Items */}
                        <div className="mb-4">
                          <div className="text-xs text-gray-500 mb-2">รายการอาหาร</div>
                          <div className="flex flex-wrap gap-2">
                            {meal.foods.map((food, index) => (
                              <span key={index} className="px-3 py-1.5 bg-gradient-to-r from-emerald-50 to-teal-50 text-emerald-700 rounded-full text-xs sm:text-sm font-medium border border-emerald-200">
                                {food}
                              </span>
                            ))}
                          </div>
                        </div>
                        {/* Calories */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Zap className="w-4 h-4 text-amber-500" />
                            <span className="text-xs sm:text-sm text-gray-600">แคลอรี่:</span>
                          </div>
                          <div className="px-3 py-2 sm:px-4 bg-gradient-to-r from-amber-100 to-orange-100 rounded-xl">
                            <span className="text-base sm:text-lg font-bold text-amber-700">{meal.calories}</span>
                            <span className="text-xs sm:text-sm text-amber-600 ml-1">kcal</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-8 text-center text-gray-500">
                    <Utensils className="w-12 h-12 mx-auto mb-3 opacity-50" />
                    <p className="font-medium">ยังไม่มี{mealType.label}</p>
                    <p className="text-sm">เริ่มบันทึกอาหารของคุณ</p>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Add Meal Modal */}
      <AddMealModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSubmit={handleAddMealSubmit}
      />

      {/* Edit Meal Modal */}
      <EditMealModal
        isOpen={showEditModal}
        onClose={handleCloseEditModal}
        onSubmit={handleEditMealSubmit}
        mealData={selectedMealForEdit}
      />

      {/* Delete Confirm Modal */}
      <DeleteConfirmModal
        isOpen={showDeleteModal}
        onClose={handleCloseDeleteModal}
        onConfirm={handleConfirmDelete}
        mealData={selectedMealForDelete}
        isDeleting={isDeleting}
      />

      {/* Image Preview Modal */}
      {imageModal && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm animate-fadein"
          onClick={() => setImageModal(null)}
        >
          <div
            className="relative bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 p-0 flex flex-col items-center border-4 border-white"
            onClick={e => e.stopPropagation()}
          >
            <button
              onClick={() => setImageModal(null)}
              className="absolute top-3 right-3 p-2 rounded-full bg-white/90 hover:bg-rose-100 text-gray-700 hover:text-rose-500 shadow transition border border-gray-200 flex items-center justify-center"
              aria-label="ปิดภาพ"
            >
              <svg xmlns='http://www.w3.org/2000/svg' className='h-6 w-6' fill='none' viewBox='0 0 24 24' stroke='currentColor'><path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M6 18L18 6M6 6l12 12' /></svg>
            </button>
            <img
              src={imageModal.url}
              alt={imageModal.alt}
              className="max-h-[70vh] w-auto rounded-xl object-contain m-0"
              style={{ boxShadow: '0 8px 32px 0 rgba(0,0,0,0.18)' }}
            />
            <div className="w-full text-center py-3 bg-white/80 rounded-b-2xl text-base text-gray-700 font-medium border-t border-gray-100 flex flex-col items-center gap-1">
              <span>{imageModal.alt}</span>
              <span className="text-xs text-gray-500">{meals.find(m => m.beforeImage === imageModal.url || m.afterImage === imageModal.url)?.time}</span>
            </div>
          </div>
        </div>
      )}
    </>
  );
}