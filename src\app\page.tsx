//\src\app\page.tsx
"use client";

import { useState, useEffect } from "react";
import { clearStoredUser } from "@/lib/auth-utils";
import LogoutConfirmModal from "@/components/LogoutConfirmModal";
import Header from "@/components/home/<USER>";
import DailyOverviewCards from "@/components/home/<USER>";
import MealSection from "@/components/home/<USER>";
import QuickActions from "@/components/home/<USER>";
import WeeklyProgress from "@/components/home/<USER>";
import NutritionTips from "@/components/home/<USER>";
import AchievementBadge from "@/components/home/<USER>";
import LoadingScreen from "@/components/LoadingScreen";

export default function NutritionHomeDashboard() {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // User data from localStorage or default
  const [userData, setUserData] = useState({
    name: "ผู้ใช้งาน",
    age: 25,
    weight: 70,
    height: 175,
    bmi: 22.9,
    goal: "health",
    dailyCalories: 2200,
    consumedCalories: 0, // เริ่มต้นที่ 0
    remainingCalories: 2200
  });

  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [sessionInfo, setSessionInfo] = useState(null);
  const [loginHistory, setLoginHistory] = useState([]);
  const [isLoadingUser, setIsLoadingUser] = useState(true);
  const [isLoadingCalories, setIsLoadingCalories] = useState(false);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // ฟังก์ชันดึงข้อมูล calories รายวัน
  const fetchDailyCalories = async (date: string) => {
    setIsLoadingCalories(true);
    try {
      const res = await fetch(`/api/food-logs?date=${date}`, {
        credentials: 'include'
      });
      
      if (res.ok) {
        const data = await res.json();
        
        if (data.foodLogs && data.foodLogs.length > 0) {
          // คำนวณ total calories จาก food logs
          const totalCalories = data.foodLogs.reduce((total: number, log: any) => {
            return total + log.items.reduce((mealTotal: number, item: any) => {
              return mealTotal + (item.calories || 0);
            }, 0);
          }, 0);

          // อัปเดต userData
          setUserData(prev => ({
            ...prev,
            consumedCalories: Math.round(totalCalories),
            remainingCalories: Math.round(prev.dailyCalories - totalCalories)
          }));

          console.log(`📊 Daily calories for ${date}: ${Math.round(totalCalories)} kcal`);
        } else {
          // ไม่มีข้อมูลในวันนี้
          setUserData(prev => ({
            ...prev,
            consumedCalories: 0,
            remainingCalories: prev.dailyCalories
          }));
          
          console.log(`📊 No meals found for ${date}`);
        }
      } else {
        console.error('Failed to fetch daily calories');
      }
    } catch (error) {
      console.error('Error fetching daily calories:', error);
    } finally {
      setIsLoadingCalories(false);
    }
  };

  // Initialize session-based authentication
  useEffect(() => {
    const checkSession = async () => {
      // ตรวจสอบ preloaded data จาก login ก่อน
      const tempUserData = sessionStorage.getItem('tempUserData');
      if (tempUserData) {
        try {
          const preloadedData = JSON.parse(tempUserData);
          setUserData(prev => ({
            ...prev,
            name: preloadedData.name,
            age: preloadedData.age,
            weight: preloadedData.weight,
            height: preloadedData.height,
            bmi: preloadedData.bmi,
            goal: preloadedData.goal,
            dailyCalories: preloadedData.dailyCalories,
            remainingCalories: preloadedData.dailyCalories // จะ update ทีหลังจาก API
          }));
          setIsLoggedIn(true);
          setIsLoadingUser(false);
          sessionStorage.removeItem('tempUserData');
          console.log('✅ Using preloaded user data:', preloadedData.name);
          return;
        } catch (error) {
          console.error('Error parsing preloaded data:', error);
        }
      }

      setIsLoadingUser(true);

      try {
        const response = await fetch('/api/session', {
          method: 'GET',
          credentials: 'include'
        });

        if (response.ok) {
          const data = await response.json();

          if (data.currentSession && data.currentSession.user) {
            const user = data.currentSession.user;

            // Update user data
            const dailyCaloriesTarget = user.tdee || 2200;
            setUserData(prev => ({
              ...prev,
              name: user.fullName || "ผู้ใช้งาน",
              age: user.age || 30,
              weight: user.weight || 70,
              height: user.height || 175,
              bmi: user.bmi || 22.9,
              goal: user.goal || "health",
              dailyCalories: dailyCaloriesTarget,
              remainingCalories: dailyCaloriesTarget // จะ update จาก fetchDailyCalories
            }));

            setIsLoggedIn(true);
            setSessionInfo(data.currentSession);
            setLoginHistory(data.loginHistory || []);

            console.log('✅ Session authenticated:', user.fullName);
          } else {
            setIsLoggedIn(false);
            console.log('❌ No active session');
          }
        } else {
          setIsLoggedIn(false);
          console.log('❌ Session check failed');
          window.location.href = '/login';
          return;
        }
      } catch (error) {
        console.error('❌ Session check error:', error);
        setIsLoggedIn(false);
        window.location.href = '/login';
        return;
      } finally {
        setIsLoadingUser(false);
      }
    };

    checkSession();
  }, []);

  // ดึงข้อมูล calories เมื่อเปลี่ยนวันที่หรือมีข้อมูลใหม่
  useEffect(() => {
    if (isLoggedIn && selectedDate) {
      fetchDailyCalories(selectedDate);
    }
  }, [isLoggedIn, selectedDate, refreshTrigger]);

  // ฟังก์ชันสำหรับ refresh ข้อมูลเมื่อมีการเปลี่ยนแปลงจาก MealSection
  const handleDataChange = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // ฟังก์ชันเปลี่ยนวันที่
  const handleDateChange = (newDate: string) => {
    setSelectedDate(newDate);
    // จะ trigger useEffect เพื่อ fetch calories ใหม่
  };

  // Show logout confirmation modal
  const handleLogoutClick = () => {
    setShowLogoutModal(true);
  };

  // Actual logout function
  const handleLogoutConfirm = async () => {
    setIsLoggingOut(true);
    try {
      await fetch('/api/login', {
        method: 'DELETE',
        credentials: 'include'
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      clearStoredUser();
      setIsLoggedIn(false);
      setSessionInfo(null);
      setLoginHistory([]);
      setIsLoggingOut(false);
      setShowLogoutModal(false);
      window.location.href = '/login';
    }
  };

  // Close logout modal
  const handleLogoutCancel = () => {
    setShowLogoutModal(false);
  };

  const handleAddMeal = (mealType: string) => {
    console.log(`เพิ่มมื้อ: ${mealType}`);
    // Navigate to food logging page
  };

  // Show loading screen while checking session
  if (isLoadingUser) {
    return <LoadingScreen message="กำลังตรวจสอบการเข้าสู่ระบบ..." />;
  }

  // Don't render main content if not logged in (will redirect)
  if (!isLoggedIn) {
    return <LoadingScreen message="กำลังนำทางไปหน้าเข้าสู่ระบบ..." />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <Header
        userName={userData.name}
        isLoadingUser={isLoadingUser}
        isLoggedIn={isLoggedIn}
        onLogoutClick={handleLogoutClick}
      />

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-8">
            
            {/* Daily Overview Cards */}
            <DailyOverviewCards 
              userData={userData} 
              isLoadingCalories={isLoadingCalories}
            />

            {/* Date Selector and Meal Sections */}
            <MealSection
              selectedDate={selectedDate}
              onDateChange={handleDateChange}
            />
          </div>

          {/* Right Column - Sidebar */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <QuickActions />

            {/* Weekly Progress */}
            <WeeklyProgress 
              selectedDate={selectedDate}
              onRefresh={refreshTrigger > 0}
            />

            {/* Nutrition Tips */}
            <NutritionTips />

            {/* Achievement Badge */}
            <AchievementBadge />
          </div>
        </div>
      </div>

      {/* Logout Confirmation Modal */}
      <LogoutConfirmModal
        isOpen={showLogoutModal}
        onClose={handleLogoutCancel}
        onConfirm={handleLogoutConfirm}
        userName={userData.name}
        isLoading={isLoggingOut}
      />
    </div>
  );
}