/*
  Warnings:

  - You are about to drop the `FFQResponse` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "FFQResponse" DROP CONSTRAINT "FFQResponse_questionId_fkey";

-- DropFore<PERSON>Key
ALTER TABLE "FFQResponse" DROP CONSTRAINT "FFQResponse_userId_fkey";

-- DropTable
DROP TABLE "FFQResponse";

-- CreateTable
CREATE TABLE "FFQSubmission" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "ffqSetId" INTEGER NOT NULL,
    "submittedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "FFQSubmission_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FFQAnswer" (
    "id" SERIAL NOT NULL,
    "submissionId" INTEGER NOT NULL,
    "questionId" INTEGER NOT NULL,
    "answer" JSONB NOT NULL,

    CONSTRAINT "FFQAnswer_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "FFQSubmission_userId_idx" ON "FFQSubmission"("userId");

-- CreateIndex
CREATE INDEX "FFQSubmission_ffqSetId_idx" ON "FFQSubmission"("ffqSetId");

-- CreateIndex
CREATE UNIQUE INDEX "FFQAnswer_submissionId_questionId_key" ON "FFQAnswer"("submissionId", "questionId");

-- AddForeignKey
ALTER TABLE "FFQSubmission" ADD CONSTRAINT "FFQSubmission_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FFQSubmission" ADD CONSTRAINT "FFQSubmission_ffqSetId_fkey" FOREIGN KEY ("ffqSetId") REFERENCES "FFQSet"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FFQAnswer" ADD CONSTRAINT "FFQAnswer_submissionId_fkey" FOREIGN KEY ("submissionId") REFERENCES "FFQSubmission"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FFQAnswer" ADD CONSTRAINT "FFQAnswer_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "FFQQuestion"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
