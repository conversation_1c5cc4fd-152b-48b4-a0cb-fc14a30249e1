# 📦 Dependencies Documentation

รายละเอียดของ libraries และ dependencies ทั้งหมดที่ใช้ในโปรเจค

## 🚀 Core Dependencies

### Framework & Runtime
- **next@15.3.3** - React framework with App Router, Server Components
- **react@19.0.0** - UI library with latest features
- **react-dom@19.0.0** - React DOM renderer

### Database & ORM
- **@prisma/client@6.8.2** - Type-safe database client
- **prisma@6.8.2** (dev) - Database toolkit and ORM

### Authentication & Security
- **iron-session@8.0.4** - Secure session management with encryption
- **bcrypt@6.0.0** - Password hashing library
- **jsonwebtoken@9.0.2** - JWT token generation and verification

### UI & Icons
- **lucide-react@0.511.0** - Beautiful SVG icons for React
- **tailwindcss@4** (dev) - Utility-first CSS framework

### TypeScript & Types
- **typescript@5** (dev) - TypeScript compiler
- **@types/node@20** (dev) - Node.js type definitions
- **@types/react@19** (dev) - React type definitions
- **@types/react-dom@19** (dev) - React DOM type definitions
- **@types/bcrypt@5.0.2** (dev) - bcrypt type definitions
- **@types/jsonwebtoken@9.0.9** (dev) - JWT type definitions

### Development Tools
- **eslint@9** (dev) - JavaScript/TypeScript linter
- **eslint-config-next@15.3.3** (dev) - ESLint config for Next.js
- **@eslint/eslintrc@3** (dev) - ESLint configuration utilities
- **@tailwindcss/postcss@4** (dev) - PostCSS plugin for Tailwind

## 🔧 Custom Libraries

### Rate Limiting System
- **Custom implementation** in `src/lib/rate-limit.ts`
- In-memory rate limiting for development
- Intelligent rate limiting (doesn't count successful attempts)
- Configurable limits per endpoint

### Session Management
- **Custom implementation** in `src/lib/session.ts`
- Built on top of iron-session
- Autofill credentials management
- Session validation and cleanup

### Authentication Utilities
- **Custom implementation** in `src/lib/auth-utils.ts`
- JWT token management
- Local/session storage utilities
- Auto-refresh functionality

## 🚫 Removed Dependencies

Libraries ที่เคยมีแต่ไม่ได้ใช้จริง:

- **@upstash/ratelimit** - ใช้ custom rate limiting แทน
- **@upstash/redis** - ใช้ in-memory storage สำหรับ development
- **redis** - ไม่จำเป็นสำหรับ development
- **connect-redis** - ไม่ได้ใช้ Redis session store
- **express-session** - ใช้ iron-session แทน
- **dotenv** - Next.js รองรับ .env โดยอัตโนมัติ
- **framer-motion** - ใช้ CSS animations แทน
- **@types/express-session** - ไม่ได้ใช้ express-session

## 📋 Installation Commands

### ติดตั้ง Dependencies ทั้งหมด
```bash
npm install
```

### ติดตั้งแยกตาม Category

#### Core Dependencies
```bash
npm install next@15.3.3 react@19.0.0 react-dom@19.0.0
```

#### Database
```bash
npm install @prisma/client@6.8.2
npm install -D prisma@6.8.2
```

#### Authentication
```bash
npm install iron-session@8.0.4 bcrypt@6.0.0 jsonwebtoken@9.0.2
npm install -D @types/bcrypt@5.0.2 @types/jsonwebtoken@9.0.9
```

#### UI & Styling
```bash
npm install lucide-react@0.511.0
npm install -D tailwindcss@4 @tailwindcss/postcss@4
```

#### Development Tools
```bash
npm install -D typescript@5 @types/node@20 @types/react@19 @types/react-dom@19
npm install -D eslint@9 eslint-config-next@15.3.3 @eslint/eslintrc@3
```

## 🔄 Update Strategy

### Major Updates
- **Next.js**: ตรวจสอบ breaking changes ใน App Router
- **React**: ทดสอบ compatibility กับ Server Components
- **Prisma**: ตรวจสอบ schema changes และ migrations

### Minor Updates
- **Security patches**: อัพเดททันทีเมื่อมี security fixes
- **Bug fixes**: อัพเดทเมื่อแก้ไข bugs ที่พบ
- **Performance**: อัพเดทเมื่อมี performance improvements

### Testing Before Update
```bash
# ตรวจสอบ outdated packages
npm outdated

# อัพเดท dependencies
npm update

# ทดสอบ build
npm run build

# ทดสอบ type checking
npx tsc --noEmit
```

## 🛡️ Security Considerations

### Regular Security Audits
```bash
# ตรวจสอบ vulnerabilities
npm audit

# แก้ไข vulnerabilities อัตโนมัติ
npm audit fix
```

### Critical Dependencies
- **bcrypt**: สำหรับ password hashing - ต้องอัพเดทเมื่อมี security patches
- **iron-session**: สำหรับ session security - ติดตาม security updates
- **jsonwebtoken**: สำหรับ JWT security - ตรวจสอบ algorithm vulnerabilities

## 📊 Bundle Size Impact

### Large Dependencies
- **next**: ~50MB (framework core)
- **react + react-dom**: ~5MB (UI library)
- **@prisma/client**: ~10MB (database client)

### Optimization
- Tree shaking enabled สำหรับ lucide-react
- Next.js automatic code splitting
- TypeScript types ไม่รวมใน production bundle

---

**หมายเหตุ**: ไฟล์นี้ควรอัพเดทเมื่อมีการเปลี่ยนแปลง dependencies
