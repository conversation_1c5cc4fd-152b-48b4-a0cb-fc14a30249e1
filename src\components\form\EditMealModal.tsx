//src\components\form\EditMealModal.tsx
"use client";

import { useState, useEffect } from "react";
import { X, Camera, Clock, Utensils, Plus, Minus, Search, Calculator, Edit3 } from "lucide-react";

interface FoodItem {
  id: string;
  name: string;
  category: string;
  unit: string;
  calories_per_unit: number;
  carbs_per_unit: number;
  protein_per_unit: number;
  fat_per_unit: number;
}

interface SelectedFood {
  food: FoodItem;
  quantity: number;
  unit: string;
}

interface FoodLogItem {
  id: string;
  foodId: number;
  amount: number;
  unit: string;
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  food: {
    id: string;
    name: string;
    category: string;
    standardUnit: string;
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
}

interface MealData {
  id: string;
  mealType: string;
  date: string;
  mealTime: string;
  beforeImage: string | null;
  afterImage: string | null;
  notes: string | null;
  items: FoodLogItem[];
}

interface EditMealModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  mealData: MealData | null;
}

export default function EditMealModal({ isOpen, onClose, onSubmit, mealData }: EditMealModalProps) {
  const [formData, setFormData] = useState({
    mealType: 'breakfast',
    date: new Date().toISOString().slice(0, 10),
    time: '',
    beforeImage: null as File | null,
    afterImage: null as File | null
  });

  const [selectedFoods, setSelectedFoods] = useState<SelectedFood[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFoodSearch, setShowFoodSearch] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showDetailInput, setShowDetailInput] = useState(false);
  const [menuDetail, setMenuDetail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [foodDatabase, setFoodDatabase] = useState<FoodItem[]>([]);
  const [foodLoading, setFoodLoading] = useState(false);
  const [foodError, setFoodError] = useState<string | null>(null);

  // เก็บ URL ของรูปเดิม
  const [existingBeforeImage, setExistingBeforeImage] = useState<string | null>(null);
  const [existingAfterImage, setExistingAfterImage] = useState<string | null>(null);

  // Prevent background scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add('overflow-hidden');
    } else {
      document.body.classList.remove('overflow-hidden');
    }
    return () => {
      document.body.classList.remove('overflow-hidden');
    };
  }, [isOpen]);

  // Populate form data when mealData changes
  useEffect(() => {
    if (mealData && isOpen) {
      const mealDate = new Date(mealData.mealTime);
      setFormData({
        mealType: mealData.mealType,
        date: mealData.date,
        time: mealDate.toTimeString().slice(0, 5),
        beforeImage: null,
        afterImage: null
      });
      
      // Set existing images
      setExistingBeforeImage(mealData.beforeImage);
      setExistingAfterImage(mealData.afterImage);
      
      // Set menu detail
      setMenuDetail(mealData.notes || '');
      setShowDetailInput(!!mealData.notes);
      
      // Convert food log items to selected foods format
      const convertedFoods: SelectedFood[] = mealData.items.map(item => ({
        food: {
          id: item.food.id,
          name: item.food.name,
          category: categoryMap[item.food.category] || item.food.category,
          unit: item.food.standardUnit,
          calories_per_unit: item.food.calories,
          carbs_per_unit: item.food.carbs,
          protein_per_unit: item.food.protein,
          fat_per_unit: item.food.fat,
        },
        quantity: item.amount,
        unit: item.unit
      }));
      
      setSelectedFoods(convertedFoods);
    }
  }, [mealData, isOpen]);

  // Mapping Prisma category (en) to UI category (th)
  const categoryMap: Record<string, string> = {
    'starch': 'แป้ง',
    'meat': 'เนื้อสัตว์',
    'vegetable': 'ผัก',
    'fruit': 'ผลไม้',
    'milk': 'นม',
    'fat': 'ไขมัน',
  };

  // Fetch food list from API
  useEffect(() => {
    if (!isOpen) return;
    setFoodLoading(true);
    setFoodError(null);
    fetch('/api/foods')
      .then(res => res.json())
      .then(data => {
        if (data.foods) {
          setFoodDatabase(
            data.foods.map((food: any) => ({
              ...food,
              category: categoryMap[food.category] || food.category,
              calories_per_unit: food.calories,
              carbs_per_unit: food.carbs,
              protein_per_unit: food.protein,
              fat_per_unit: food.fat,
              unit: food.standardUnit,
            }))
          );
        } else setFoodDatabase([]);
      })
      .catch(() => setFoodError('ไม่สามารถโหลดรายการอาหารได้'))
      .finally(() => setFoodLoading(false));
  }, [isOpen]);

  const mealTypes = [
    { value: 'breakfast', label: 'มื้อเช้า', color: 'from-orange-400 to-amber-500', bgColor: 'bg-orange-50', textColor: 'text-orange-700' },
    { value: 'lunch', label: 'มื้อกลางวัน', color: 'from-yellow-400 to-orange-500', bgColor: 'bg-yellow-50', textColor: 'text-orange-700' },
    { value: 'dinner', label: 'มื้อเย็น', color: 'from-purple-400 to-pink-500', bgColor: 'bg-purple-50', textColor: 'text-purple-700' },
    { value: 'snack', label: 'ของว่าง', color: 'from-blue-400 to-cyan-500', bgColor: 'bg-blue-50', textColor: 'text-blue-700' }
  ];

  const foodCategories = [
    { name: 'แป้ง', color: 'bg-yellow-100 text-yellow-800', icon: '🍚' },
    { name: 'เนื้อสัตว์', color: 'bg-red-100 text-red-800', icon: '🥩' },
    { name: 'ผัก', color: 'bg-green-100 text-green-800', icon: '🥬' },
    { name: 'ผลไม้', color: 'bg-pink-100 text-pink-800', icon: '🍎' },
    { name: 'นม', color: 'bg-blue-100 text-blue-800', icon: '🥛' },
    { name: 'ไขมัน', color: 'bg-purple-100 text-purple-800', icon: '🧈' }
  ];

  const standardUnits = ['ทัพพี', 'ช้อนโต๊ะ', 'ช้อนชา', 'ถ้วยตวง', 'แก้ว', 'ลูก', 'ชิ้น'];

  const filteredFoods = searchTerm
    ? foodDatabase.filter(food =>
        food.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        food.category.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : selectedCategory
      ? foodDatabase.filter(food => food.category === selectedCategory)
      : [];

  const addFood = (food: FoodItem) => {
    const existing = selectedFoods.find(item => item.food.id === food.id);
    if (existing) {
      setSelectedFoods(selectedFoods.map(item =>
        item.food.id === food.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setSelectedFoods([...selectedFoods, { food, quantity: 1, unit: food.unit }]);
    }
    setShowFoodSearch(false);
    setSearchTerm('');
  };

  const updateFoodQuantity = (foodId: string, quantity: number) => {
    if (quantity <= 0) {
      setSelectedFoods(selectedFoods.filter(item => item.food.id !== foodId));
    } else {
      setSelectedFoods(selectedFoods.map(item =>
        item.food.id === foodId ? { ...item, quantity } : item
      ));
    }
  };

  const updateFoodUnit = (foodId: string, unit: string) => {
    setSelectedFoods(selectedFoods.map(item =>
      item.food.id === foodId ? { ...item, unit } : item
    ));
  };

  const calculateNutrition = () => {
    return selectedFoods.reduce(
      (total, item) => ({
        calories: total.calories + (item.food.calories_per_unit * item.quantity),
        carbs: total.carbs + (item.food.carbs_per_unit * item.quantity),
        protein: total.protein + (item.food.protein_per_unit * item.quantity),
        fat: total.fat + (item.food.fat_per_unit * item.quantity)
      }),
      { calories: 0, carbs: 0, protein: 0, fat: 0 }
    );
  };

  const nutrition = calculateNutrition();

  // ฟังก์ชันอัปโหลดไฟล์ไป Cloudinary ผ่าน API
  async function uploadImageToCloudinary(file: File): Promise<string | null> {
    const formData = new FormData();
    formData.append('file', file);
    const res = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });
    if (res.ok) {
      const data = await res.json();
      return data.url;
    }
    return null;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!mealData) return;
    
    setLoading(true);
    setError(null);
    setSuccess(false);
    
    let beforeImageUrl = existingBeforeImage;
    let afterImageUrl = existingAfterImage;
    
    try {
      // อัปโหลดรูปใหม่ถ้ามี
      if (formData.beforeImage) {
        beforeImageUrl = await uploadImageToCloudinary(formData.beforeImage);
        if (!beforeImageUrl) throw new Error('อัปโหลดรูปก่อนทานไม่สำเร็จ');
      }
      if (formData.afterImage) {
        afterImageUrl = await uploadImageToCloudinary(formData.afterImage);
        if (!afterImageUrl) throw new Error('อัปโหลดรูปหลังทานไม่สำเร็จ');
      }
      
      const updateData = {
        ...formData,
        foods: selectedFoods.map(item => `${item.food.name} ${item.quantity} ${item.unit}`),
        selectedFoods,
        nutrition,
        calories: Math.round(nutrition.calories),
        beforeImage: beforeImageUrl,
        afterImage: afterImageUrl,
        menuDetail: menuDetail.trim(),
      };
      
      const res = await fetch(`/api/food-logs/${mealData.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData),
      });
      
      if (!res.ok) throw new Error('อัปเดตข้อมูลไม่สำเร็จ');
      
      setSuccess(true);
      if (onSubmit) onSubmit(await res.json());
      onClose();
      
    } catch (err: any) {
      setError(err.message || 'เกิดข้อผิดพลาด');
    } finally {
      setLoading(false);
    }
  };

  const handleImageUpload = (type: 'beforeImage' | 'afterImage', file: File | null) => {
    setFormData(prev => ({ ...prev, [type]: file }));
  };

  const selectedMealType = mealTypes.find(type => type.value === formData.mealType);

  // รูปที่จะแสดง - ถ้ามีรูปใหม่ใช้รูปใหม่ ถ้าไม่มีใช้รูปเดิม
  const beforeImageUrl = formData.beforeImage 
    ? URL.createObjectURL(formData.beforeImage) 
    : existingBeforeImage;
  const afterImageUrl = formData.afterImage 
    ? URL.createObjectURL(formData.afterImage) 
    : existingAfterImage;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-start sm:items-center justify-center p-3 overflow-hidden">
      <div className="bg-white rounded-2xl w-full max-w-[95vw] sm:max-w-4xl lg:max-w-6xl my-3 sm:my-4 overflow-hidden shadow-2xl flex flex-col max-h-[90vh]">
        
        {/* Header */}
        <div className={`${selectedMealType?.bgColor} p-4 sm:p-5 border-b border-gray-100 flex-shrink-0`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 sm:gap-4">
              <div className={`w-10 h-10 sm:w-12 h-12 bg-gradient-to-r ${selectedMealType?.color} rounded-lg flex items-center justify-center shadow-md`}>
                <Edit3 className="w-5 h-5 sm:w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-lg sm:text-xl font-bold text-gray-900">แก้ไขข้อมูลอาหาร</h3>
                <p className="text-sm sm:text-base text-gray-600">{selectedMealType?.label}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-white/50 rounded-lg transition-colors"
            >
              <X className="w-5 h-5 sm:w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex flex-col lg:flex-row flex-1 min-h-0 overflow-y-auto">
          
          {/* Food Selection Section */}
          <div className="w-full lg:w-2/3 p-4 sm:p-5 border-b lg:border-b-0 lg:border-r border-gray-200 flex-1 overflow-y-auto">
            
            {/* Meal Type & Time */}
            <div className="mb-6 sm:mb-8">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                
                {/* Meal Type */}
                <div>
                  <label className="block text-sm sm:text-base font-semibold text-gray-700 mb-2">ประเภทมื้อ</label>
                  <div className="grid grid-cols-2 gap-3">
                    {mealTypes.map(type => (
                      <button
                        key={type.value}
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, mealType: type.value }))}
                        className={`p-3 sm:p-3.5 rounded-lg border-2 transition-all duration-300 text-sm sm:text-base font-medium ${
                          formData.mealType === type.value
                            ? `${type.bgColor} ${type.textColor} border-current shadow-sm`
                            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-700'
                        }`}
                      >
                        {type.label}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Date & Time */}
                <div>
                  <label className="block text-sm sm:text-base font-semibold text-gray-900 mb-2">
                    <Clock className="w-4 h-4 sm:w-5 h-5 inline mr-2" />
                    วันและเวลา
                  </label>
                  <div className="flex gap-2 mb-3">
                    <input
                      type="date"
                      value={formData.date}
                      onChange={e => setFormData(prev => ({ ...prev, date: e.target.value }))}
                      className="w-1/2 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-gray-900 text-sm sm:text-base"
                      max={new Date().toISOString().slice(0, 10)}
                      required
                    />
                    <input
                      type="time"
                      value={formData.time}
                      onChange={e => setFormData(prev => ({ ...prev, time: e.target.value }))}
                      className="w-1/2 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-gray-900 text-sm sm:text-base"
                      style={{ colorScheme: 'light' }}
                      required
                    />
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      const now = new Date();
                      const dateString = now.toISOString().slice(0, 10);
                      const timeString = now.toTimeString().slice(0, 5);
                      setFormData(prev => ({ ...prev, date: dateString, time: timeString }));
                    }}
                    className="w-full py-2.5 px-4 text-sm sm:text-base bg-emerald-50 text-emerald-700 rounded-lg hover:bg-emerald-100 transition-colors border border-emerald-200"
                  >
                    ใช้วันและเวลาปัจจุบัน ({new Date().toLocaleDateString('th-TH')} {new Date().toTimeString().slice(0, 5)})
                  </button>
                </div>
              </div>
            </div>

            {/* Food Search & Category Dropdown */}
            <div className="mb-6 sm:mb-8">
              <div className="flex flex-col sm:flex-row gap-3 items-stretch">
                <div className="flex-1">
                  <label className="block text-sm sm:text-base font-semibold text-gray-900 mb-2">
                    <Search className="w-4 h-4 sm:w-5 h-5 inline mr-2" />
                    ค้นหาอาหาร
                  </label>
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value);
                      setShowFoodSearch(e.target.value.length > 0);
                      setSelectedCategory('');
                    }}
                    placeholder="พิมพ์ชื่ออาหารหรือประเภท..."
                    className="w-full px-3 sm:px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors text-gray-900 placeholder-gray-500 text-sm sm:text-base"
                  />
                </div>
                <div className="w-full sm:w-56 flex-shrink-0">
                  <label className="block text-sm sm:text-base font-semibold text-gray-900 mb-2">เลือกหมวดหมู่</label>
                  <select
                    value={selectedCategory}
                    onChange={e => {
                      setSelectedCategory(e.target.value);
                      setSearchTerm('');
                      setShowFoodSearch(true);
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-gray-900 text-sm sm:text-base"
                  >
                    <option value="">-- เลือกหมวดหมู่ --</option>
                    {foodCategories.map(cat => (
                      <option key={cat.name} value={cat.name}>{cat.icon} {cat.name}</option>
                    ))}
                  </select>
                </div>
              </div>
              <div className="relative mt-2">
                {showFoodSearch && filteredFoods.length > 0 && (
                  <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-20 max-h-48 overflow-y-auto mt-1">
                    {filteredFoods.map(food => {
                      const category = foodCategories.find(cat => cat.name === food.category);
                      return (
                        <button
                          key={food.id}
                          onClick={() => addFood(food)}
                          className="w-full p-3 sm:p-4 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <span className="text-lg sm:text-xl">{category?.icon}</span>
                              <div>
                                <div className="font-medium text-gray-900 text-sm sm:text-base">{food.name}</div>
                                <div className="text-sm text-gray-700">
                                  {food.calories_per_unit} แคลอรี่/{food.unit}
                                </div>
                              </div>
                            </div>
                            <span className={`px-2 py-1 rounded-full text-sm font-medium ${category?.color}`}>
                              {food.category}
                            </span>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>

            {/* Selected Foods */}
            <div className="mb-6 sm:mb-8">
              <label className="block text-sm sm:text-base font-semibold text-gray-900 mb-2">
                รายการอาหารที่เลือก ({selectedFoods.length} รายการ)
              </label>
              {selectedFoods.length > 0 ? (
                <div className="space-y-4">
                  {selectedFoods.map(item => {
                    const category = foodCategories.find(cat => cat.name === item.food.category);
                    return (
                      <div key={item.food.id} className="bg-gray-50 rounded-lg p-3 sm:p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-3 flex-1">
                            <span className="text-lg sm:text-xl">{category?.icon}</span>
                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-gray-900 text-sm sm:text-base truncate">{item.food.name}</div>
                              <span className={`inline-block px-2 py-1 rounded-full text-sm font-medium ${category?.color} mt-1`}>
                                {item.food.category}
                              </span>
                            </div>
                          </div>
                          <button
                            onClick={() => updateFoodQuantity(item.food.id, 0)}
                            className="text-red-500 hover:text-red-700 p-1.5 ml-2"
                          >
                            <X className="w-4 h-4 sm:w-5 h-5" />
                          </button>
                        </div>
                        
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                          <div>
                            <label className="block text-sm text-gray-900 mb-1">ปริมาณ</label>
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => updateFoodQuantity(item.food.id, Math.max(0, item.quantity - 0.5))}
                                className="p-1.5 hover:bg-gray-200 rounded text-gray-900 transition-colors"
                              >
                                <Minus className="w-4 h-4 sm:w-5 h-5" />
                              </button>
                              <input
                                type="number"
                                value={item.quantity}
                                onChange={(e) => updateFoodQuantity(item.food.id, parseFloat(e.target.value) || 0)}
                                className="flex-1 px-3 py-2 border border-gray-300 rounded text-center text-gray-900 text-sm sm:text-base"
                                step="0.5"
                                min="0"
                              />
                              <button
                                onClick={() => updateFoodQuantity(item.food.id, item.quantity + 0.5)}
                                className="p-1.5 hover:bg-gray-200 rounded text-gray-900 transition-colors"
                              >
                                <Plus className="w-4 h-4 sm:w-5 h-5" />
                              </button>
                            </div>
                          </div>
                          
                          <div>
                            <label className="block text-sm text-gray-900 mb-1">หน่วย</label>
                            <select
                              value={item.unit}
                              onChange={(e) => updateFoodUnit(item.food.id, e.target.value)}
                              className="w-full px-3 py-2 border border-gray-300 rounded text-gray-900 text-sm sm:text-base"
                            >
                              {standardUnits.map(unit => (
                                <option key={unit} value={unit}>{unit}</option>
                              ))}
                            </select>
                          </div>
                        </div>
                        
                        <div className="mt-3 text-sm sm:text-base text-gray-800 font-medium">
                          {Math.round(item.food.calories_per_unit * item.quantity)} แคลอรี่
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-600">
                  <Utensils className="w-10 h-10 mx-auto mb-3 opacity-50" />
                  <p className="font-medium text-sm sm:text-base">ยังไม่ได้เลือกอาหาร</p>
                  <p className="text-sm text-gray-500">ค้นหาและเลือกอาหารด้านบน</p>
                </div>
              )}
            </div>

            {/* Images Upload */}
            <div className="mb-6 sm:mb-8">
              <label className="block text-sm sm:text-base font-semibold text-gray-900 mb-2">
                <Camera className="w-4 h-4 sm:w-5 h-5 inline mr-2" />
                รูปภาพ
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {/* Before Image */}
                <div>
                  <label className="block text-sm text-gray-900 mb-1">รูปก่อนทาน</label>
                  <div className="relative group">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => handleImageUpload('beforeImage', e.target.files?.[0] || null)}
                      className="hidden"
                      id="beforeImage"
                    />
                    <label 
                      htmlFor="beforeImage" 
                      className="block border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-emerald-400 hover:bg-emerald-50 transition-all cursor-pointer group-hover:shadow-md"
                    >
                      {beforeImageUrl ? (
                        <div className="relative">
                          <img src={beforeImageUrl} alt="before preview" className="mx-auto mb-3 rounded-lg max-h-24 object-contain" />
                          {formData.beforeImage && (
                            <div className="absolute -top-2 -right-2 bg-emerald-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs">
                              ใหม่
                            </div>
                          )}
                        </div>
                      ) : (
                        <Camera className="w-6 h-6 text-gray-400 mx-auto mb-3 group-hover:text-emerald-500 transition-colors" />
                      )}
                      <div className="text-sm text-gray-700 group-hover:text-emerald-600">
                        {formData.beforeImage ? (
                          <span className="text-emerald-600 font-medium">{formData.beforeImage.name}</span>
                        ) : beforeImageUrl ? (
                          <span className="text-gray-600">คลิกเพื่อเปลี่ยนรูป</span>
                        ) : (
                          'เลือกรูป'
                        )}
                      </div>
                    </label>
                  </div>
                </div>

                {/* After Image */}
                <div>
                  <label className="block text-sm text-gray-900 mb-1">รูปหลังทาน</label>
                  <div className="relative group">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => handleImageUpload('afterImage', e.target.files?.[0] || null)}
                      className="hidden"
                      id="afterImage"
                    />
                    <label 
                      htmlFor="afterImage" 
                      className="block border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 hover:bg-blue-50 transition-all cursor-pointer group-hover:shadow-md"
                    >
                      {afterImageUrl ? (
                        <div className="relative">
                          <img src={afterImageUrl} alt="after preview" className="mx-auto mb-3 rounded-lg max-h-24 object-contain" />
                          {formData.afterImage && (
                            <div className="absolute -top-2 -right-2 bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs">
                              ใหม่
                            </div>
                          )}
                        </div>
                      ) : (
                        <Camera className="w-6 h-6 text-gray-400 mx-auto mb-3 group-hover:text-blue-500 transition-colors" />
                      )}
                      <div className="text-sm text-gray-700 group-hover:text-blue-600">
                        {formData.afterImage ? (
                          <span className="text-blue-600 font-medium">{formData.afterImage.name}</span>
                        ) : afterImageUrl ? (
                          <span className="text-gray-600">คลิกเพื่อเปลี่ยนรูป (ไม่บังคับ)</span>
                        ) : (
                          'เลือกรูป (ไม่บังคับ)'
                        )}
                      </div>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Menu Detail Section */}
            <div className="mb-6 sm:mb-8">
              <div className="flex items-center gap-2 mb-2">
                <span className="block text-sm sm:text-base font-semibold text-gray-900">บอกรายละเอียดเพิ่มเติม</span>
                <button
                  type="button"
                  onClick={() => setShowDetailInput(v => !v)}
                  className="ml-2 px-2 py-1 text-xs sm:text-sm rounded bg-gray-100 hover:bg-emerald-100 text-emerald-700 border border-emerald-200 transition-colors"
                >
                  {showDetailInput ? 'ซ่อน' : 'เพิ่มรายละเอียด'}
                </button>
              </div>
              {showDetailInput ? (
                <input
                  type="text"
                  value={menuDetail}
                  onChange={e => setMenuDetail(e.target.value)}
                  placeholder="เช่น วิธีปรุง, วัตถุดิบ, หมายเหตุ ฯลฯ"
                  className="w-full px-3 py-2 border border-gray-300 rounded text-gray-900 text-sm sm:text-base"
                  maxLength={200}
                />
              ) : (
                <div className="text-gray-500 text-sm">(ถ้าต้องการใส่รายละเอียดเมนูเพิ่มเติม กดปุ่มด้านขวา)</div>
              )}
            </div>

            {/* Nutrition Summary (Mobile Only) */}
            <div className="lg:hidden p-4 sm:p-5 bg-gray-50">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Calculator className="w-5 h-5 sm:w-6 h-6 text-emerald-600" />
                  <h4 className="font-bold text-gray-900 text-base sm:text-lg">สรุปโภชนาการ</h4>
                </div>

                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <div className="text-center">
                    <div className="text-2xl sm:text-3xl font-bold text-emerald-600 mb-2">
                      {Math.round(nutrition.calories)}
                    </div>
                    <div className="text-sm sm:text-base text-gray-600">แคลอรี่รวม</div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-3">
                      <div 
                        className="bg-gradient-to-r from-emerald-500 to-teal-600 h-2 rounded-full transition-all duration-500"
                        style={{ width: Math.min(100, (nutrition.calories / 500) * 100) + '%' }}
                      ></div>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <h5 className="font-semibold text-gray-900 text-sm sm:text-base mb-3">สารอาหารหลัก</h5>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm sm:text-base text-gray-800">คาร์โบไฮเดรต</span>
                      <div className="flex items-center gap-2">
                        <div className="w-14 bg-yellow-100 rounded-full h-2">
                          <div 
                            className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: Math.min(100, (nutrition.carbs / 50) * 100) + '%' }}
                          ></div>
                        </div>
                        <span className="font-medium text-gray-900 text-sm sm:text-base w-8 text-right">{Math.round(nutrition.carbs)}g</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm sm:text-base text-gray-800">โปรตีน</span>
                      <div className="flex items-center gap-2">
                        <div className="w-14 bg-red-100 rounded-full h-2">
                          <div 
                            className="bg-red-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: Math.min(100, (nutrition.protein / 30) * 100) + '%' }}
                          ></div>
                        </div>
                        <span className="font-medium text-gray-900 text-sm sm:text-base w-8 text-right">{Math.round(nutrition.protein)}g</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm sm:text-base text-gray-800">ไขมัน</span>
                      <div className="flex items-center gap-2">
                        <div className="w-14 bg-purple-100 rounded-full h-2">
                          <div 
                            className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: Math.min(100, (nutrition.fat / 25) * 100) + '%' }}
                          ></div>
                        </div>
                        <span className="font-medium text-gray-900 text-sm sm:text-base w-8 text-right">{Math.round(nutrition.fat)}g</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <h5 className="font-semibold text-gray-900 text-sm sm:text-base mb-3">หมวดหมู่อาหาร</h5>
                  <div className="grid grid-cols-2 gap-3">
                    {foodCategories.map(category => {
                      const count = selectedFoods.filter(item => item.food.category === category.name).length;
                      return (
                        <div key={category.name} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <span className="text-lg sm:text-xl">{category.icon}</span>
                            <span className="text-sm sm:text-base text-gray-600">{category.name}</span>
                          </div>
                          <span className={`px-2 py-1 rounded-full text-sm font-medium ${
                            count > 0 ? category.color : 'bg-gray-100 text-gray-500'
                          }`}>
                            {count}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Nutrition Summary (Desktop Only) */}
          <div className="hidden lg:block w-1/3 p-4 sm:p-5 bg-gray-50 flex-shrink-0 overflow-y-auto">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Calculator className="w-5 h-5 sm:w-6 h-6 text-emerald-600" />
                <h4 className="font-bold text-gray-900 text-base sm:text-lg">สรุปโภชนาการ</h4>
              </div>

              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="text-center">
                  <div className="text-2xl sm:text-3xl font-bold text-emerald-600 mb-2">
                    {Math.round(nutrition.calories)}
                  </div>
                  <div className="text-sm sm:text-base text-gray-600">แคลอรี่รวม</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-3">
                    <div 
                      className="bg-gradient-to-r from-emerald-500 to-teal-600 h-2 rounded-full transition-all duration-500"
                      style={{ width: Math.min(100, (nutrition.calories / 500) * 100) + '%' }}
                    ></div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-4 shadow-sm">
                <h5 className="font-semibold text-gray-900 text-sm sm:text-base mb-3">สารอาหารหลัก</h5>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm sm:text-base text-gray-800">คาร์โบไฮเดรต</span>
                    <div className="flex items-center gap-2">
                      <div className="w-14 bg-yellow-100 rounded-full h-2">
                        <div 
                          className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: Math.min(100, (nutrition.carbs / 50) * 100) + '%' }}
                        ></div>
                      </div>
                      <span className="font-medium text-gray-900 text-sm sm:text-base w-8 text-right">{Math.round(nutrition.carbs)}g</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm sm:text-base text-gray-800">โปรตีน</span>
                    <div className="flex items-center gap-2">
                      <div className="w-14 bg-red-100 rounded-full h-2">
                        <div 
                          className="bg-red-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: Math.min(100, (nutrition.protein / 30) * 100) + '%' }}
                        ></div>
                      </div>
                      <span className="font-medium text-gray-900 text-sm sm:text-base w-8 text-right">{Math.round(nutrition.protein)}g</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm sm:text-base text-gray-800">ไขมัน</span>
                    <div className="flex items-center gap-2">
                      <div className="w-14 bg-purple-100 rounded-full h-2">
                        <div 
                          className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: Math.min(100, (nutrition.fat / 25) * 100) + '%' }}
                        ></div>
                      </div>
                      <span className="font-medium text-gray-900 text-sm sm:text-base w-8 text-right">{Math.round(nutrition.fat)}g</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg p-4 shadow-sm">
                <h5 className="font-semibold text-gray-900 text-sm sm:text-base mb-3">หมวดหมู่อาหาร</h5>
                <div className="grid grid-cols-2 gap-3">
                  {foodCategories.map(category => {
                    const count = selectedFoods.filter(item => item.food.category === category.name).length;
                    return (
                      <div key={category.name} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-lg sm:text-xl">{category.icon}</span>
                          <span className="text-sm sm:text-base text-gray-600">{category.name}</span>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-sm font-medium ${
                          count > 0 ? category.color : 'bg-gray-100 text-gray-500'
                        }`}>
                          {count}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Buttons */}
        <div className="p-4 sm:p-5 border-t border-gray-100 bg-gray-50 flex-shrink-0">
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 py-3 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors font-medium text-sm sm:text-base"
            >
              ยกเลิก
            </button>
            <button
              onClick={handleSubmit}
              disabled={selectedFoods.length === 0 || !formData.time || loading}
              className="flex-1 py-3 px-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all shadow-md hover:shadow-lg font-medium text-sm sm:text-base disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'กำลังอัปเดต...' : `อัปเดตข้อมูล (${Math.round(nutrition.calories)} แคลอรี่)`}
            </button>
          </div>
        </div>

        {/* Error/Success Messages */}
        {error && <div className="text-red-600 text-center mt-2">{error}</div>}
        {success && <div className="text-blue-600 text-center mt-2">อัปเดตสำเร็จ!</div>}
      </div>
    </div>
  );
}