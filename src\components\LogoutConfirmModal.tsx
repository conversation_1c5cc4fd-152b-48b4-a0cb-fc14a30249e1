// src/components/LogoutConfirmModal.tsx
"use client";

import { useEffect, useState } from 'react';
import { X, LogOut, AlertTriangle } from 'lucide-react';

interface LogoutConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  userName?: string;
  isLoading?: boolean;
}

export default function LogoutConfirmModal({ 
  isOpen, 
  onClose, 
  onConfirm, 
  userName = "ผู้ใช้งาน",
  isLoading = false 
}: LogoutConfirmModalProps) {
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setShow(true);
    }
  }, [isOpen]);

  const handleClose = () => {
    if (isLoading) return;
    setShow(false);
    setTimeout(onClose, 150);
  };

  const handleConfirm = () => {
    if (isLoading) return;
    onConfirm();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className={`absolute inset-0 bg-black/20 backdrop-blur-sm transition-all duration-200 ${
          show ? 'opacity-100' : 'opacity-0'
        }`}
        onClick={handleClose}
      />
      
      {/* Modal */}
      <div className={`relative bg-white rounded-3xl shadow-2xl border border-gray-100 max-w-md w-full mx-4 transform transition-all duration-300 ${
        show ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
      }`}>
        
        {/* Header */}
        <div className="p-6 pb-4">
          <div className="flex items-center justify-between mb-4">
            <div className={`flex items-center gap-3 transition-all duration-300 delay-100 ${
              show ? 'translate-x-0 opacity-100' : '-translate-x-4 opacity-0'
            }`}>
              <div className="w-12 h-12 bg-gradient-to-br from-red-400 to-red-500 rounded-2xl flex items-center justify-center shadow-lg">
                <LogOut className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-800">ยืนยันการออกจากระบบ</h3>
                <p className="text-sm text-gray-500">กรุณายืนยันการดำเนินการ</p>
              </div>
            </div>
            
            {!isLoading && (
              <button
                onClick={handleClose}
                className={`p-2 hover:bg-gray-100 rounded-xl transition-all duration-300 delay-200 ${
                  show ? 'translate-x-0 opacity-100 rotate-0' : 'translate-x-4 opacity-0 rotate-90'
                }`}
              >
                <X className="w-5 h-5 text-gray-400" />
              </button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="px-6 pb-6">
          {/* Warning Message */}
          <div className={`bg-amber-50 border border-amber-200 rounded-2xl p-4 mb-6 transition-all duration-300 delay-200 ${
            show ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
          }`}>
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-amber-800 mb-1">คุณกำลังจะออกจากระบบ</h4>
                <p className="text-sm text-amber-700">
                  สวัสดี <span className="font-medium">{userName}</span> คุณแน่ใจหรือไม่ว่าต้องการออกจากระบบ?
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className={`flex gap-3 transition-all duration-300 delay-300 ${
            show ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
          }`}>
            <button
              onClick={handleClose}
              disabled={isLoading}
              className="flex-1 px-4 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105"
            >
              ยกเลิก
            </button>
            
            <button
              onClick={handleConfirm}
              disabled={isLoading}
              className="flex-1 px-4 py-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 hover:scale-105"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  กำลังออกจากระบบ...
                </>
              ) : (
                <>
                  <LogOut className="w-4 h-4" />
                  ออกจากระบบ
                </>
              )}
            </button>
          </div>
        </div>

        {/* Footer */}
        <div className={`px-6 py-4 bg-gray-50/50 rounded-b-3xl border-t border-gray-100 transition-all duration-300 delay-400 ${
          show ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
        }`}>
          <p className="text-xs text-gray-500 text-center">
            การออกจากระบบจะลบข้อมูล session ปัจจุบันของคุณ
          </p>
        </div>
      </div>
    </div>
  );
}