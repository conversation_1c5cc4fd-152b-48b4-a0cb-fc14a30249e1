import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET() {
  try {
    const questions = await prisma.fFQQuestion.findMany({
      orderBy: { id: 'asc' }
    });
    // Return all fields as-is for editing
    return NextResponse.json({ questions });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch FFQ questions' }, { status: 500 });
  }
}

export async function DELETE(req: Request) {
  const { searchParams } = new URL(req.url);
  const id = Number(searchParams.get('id'));
  if (!id) return NextResponse.json({ error: 'Missing id' }, { status: 400 });
  try {
    // Delete all related FFQResponse first
    await prisma.fFQResponse.deleteMany({ where: { questionId: id } });
    // Then delete the question
    await prisma.fFQQuestion.delete({ where: { id } });
    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to delete FFQ question' }, { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    if (!body.ffqSetId) {
      return NextResponse.json({ error: 'Missing ffqSetId' }, { status: 400 });
    }
    const created = await prisma.fFQQuestion.create({
      data: {
        question: body.question,
        foodGroup: body.foodGroup,
        order: Number(body.order) || 1,
        options: body.options || [],
        type: body.type || 'single',
        isActive: body.isActive !== false,
        ffqSetId: Number(body.ffqSetId),
      },
    });
    return NextResponse.json({ question: created });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create FFQ question' }, { status: 500 });
  }
}

export async function PUT(req: Request) {
  try {
    const body = await req.json();
    if (!body.id) return NextResponse.json({ error: 'Missing id' }, { status: 400 });
    const updated = await prisma.fFQQuestion.update({
      where: { id: Number(body.id) },
      data: {
        question: body.question,
        foodGroup: body.foodGroup,
        order: Number(body.order) || 1,
        options: body.options || [],
        type: body.type || 'single',
        isActive: body.isActive !== false,
        ffqSetId: body.ffqSetId ? Number(body.ffqSetId) : undefined,
      },
    });
    return NextResponse.json({ question: updated });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to update FFQ question' }, { status: 500 });
  }
}
