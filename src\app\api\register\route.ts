// C:\SofterM Github\project-nutrition\src\app\api\register\route.ts
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';
import { createRateLimiter, createRateLimitResponse, addRateLimitHeaders } from '@/lib/rate-limit';
import { createSession, type SessionUser } from '@/lib/session';

const prisma = new PrismaClient();

function calculateBMR(weight: number, height: number, age: number, gender: string): number {
  if (gender === 'male') {
    return 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age);
  } else if (gender === 'female') {
    return 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age);
  } else {
    const maleResult = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age);
    const femaleResult = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age);
    return (maleResult + femaleResult) / 2;
  }
}

function calculateTDEE(bmr: number, activityLevel: string): number {
  const multipliers = { 
    sedentary: 1.2, 
    light: 1.375, 
    moderate: 1.55, 
    active: 1.725, 
    very_active: 1.9 
  };
  return bmr * (multipliers[activityLevel as keyof typeof multipliers] || 1.55);
}

function calculateAge(birthDate: Date): number {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
}

export async function POST(req: NextRequest) {
  try {
    console.log('🚀 Registration API called');

    // Rate limiting check
    const registerLimiter = createRateLimiter('register');
    const rateLimitResult = registerLimiter(req);

    if (!rateLimitResult.success) {
      console.log('🚫 Rate limit exceeded for registration');
      return createRateLimitResponse(rateLimitResult);
    }

    const body = await req.json();
    console.log('📝 Request:', { email: body.email, fullName: body.fullName });
    
    const { 
      email, 
      password, 
      fullName, 
      gender, 
      birthDate, 
      height, 
      weight, 
      activityLevel, 
      goal, 
      foodAllergies 
    } = body;

    // Validation
    if (!email || !password || !fullName || !gender || !birthDate || !height || !weight || !activityLevel || !goal) {
      return NextResponse.json({ 
        error: 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน' 
      }, { status: 400 });
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({ 
        error: 'รูปแบบอีเมลไม่ถูกต้อง' 
      }, { status: 400 });
    }

    if (password.length < 8) {
      return NextResponse.json({ 
        error: 'รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร' 
      }, { status: 400 });
    }

    const heightNum = parseFloat(height);
    const weightNum = parseFloat(weight);
    const birthDateObj = new Date(birthDate);
    const age = calculateAge(birthDateObj);

    if (age < 5 || age > 120) {
      return NextResponse.json({ 
        error: `อายุต้องอยู่ระหว่าง 5-120 ปี (อายุปัจจุบัน: ${age} ปี)` 
      }, { status: 400 });
    }

    // Check existing user
    const existingUser = await prisma.user.findUnique({ 
      where: { email } 
    });
    
    if (existingUser) {
      return NextResponse.json({ 
        error: 'อีเมลนี้ถูกใช้งานแล้ว กรุณาใช้อีเมลอื่น' 
      }, { status: 400 });
    }

    // Calculate values
    const hashedPassword = await bcrypt.hash(password, 12);
    const bmr = Math.round(calculateBMR(weightNum, heightNum, age, gender));
    const tdee = Math.round(calculateTDEE(bmr, activityLevel));
    
    // Insert user (without dietaryPreferences)
    await prisma.$executeRaw`
      INSERT INTO "User" (
        email, password, "fullName", gender, "birthDate", 
        height, weight, "activityLevel", goal, "foodAllergies", 
        bmr, tdee, role, "isActive", "createdAt", "updatedAt"
      ) VALUES (
        ${email.toLowerCase()},
        ${hashedPassword},
        ${fullName},
        ${gender},
        ${birthDateObj},
        ${heightNum},
        ${weightNum},
        ${activityLevel},
        ${goal},
        ${foodAllergies || null},
        ${bmr},
        ${tdee},
        'user',
        true,
        NOW(),
        NOW()
      )
    `;

    console.log('✅ User created successfully');

    // Get the created user for auto login
    const newUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase() }
    });

    if (!newUser) {
      throw new Error('Failed to retrieve created user');
    }

    // Calculate BMI for session
    const bmi = Math.round((weightNum / Math.pow(heightNum / 100, 2)) * 10) / 10;

    // Create session user object for auto login
    const sessionUser: SessionUser = {
      id: newUser.id,
      email: newUser.email,
      fullName: newUser.fullName,
      gender: newUser.gender,
      height: newUser.height,
      weight: newUser.weight,
      activityLevel: newUser.activityLevel,
      goal: newUser.goal,
      foodAllergies: newUser.foodAllergies || undefined,
      bmr: newUser.bmr || undefined,
      tdee: newUser.tdee || undefined,
      role: newUser.role,
      bmi,
      age,
      loginTime: Date.now(),
      lastActivity: Date.now()
    };

    const response = NextResponse.json({
      message: 'ลงทะเบียนสำเร็จ! ยินดีต้อนรับสู่ระบบบันทึกโภชนาการ 🎉',
      user: sessionUser,
      autoLogin: true,
      sessionBased: true
    }, { status: 201 });

    // Auto login: Create session for the new user
    await createSession(
      req,
      response,
      sessionUser,
      { email: email.toLowerCase(), password, rememberMe: false }, // Default to session-only for new users
      req.headers.get('user-agent') || undefined
    );

    // Add rate limit headers to successful response
    addRateLimitHeaders(response, rateLimitResult);

    console.log('✅ Auto login completed for new user:', email.toLowerCase());

    return response;

  } catch (error: any) {
    console.error('❌ Error:', error.message);
    
    if (error.message?.includes('unique') || error.code === 'P2002') {
      return NextResponse.json({ 
        error: 'อีเมลนี้ถูกใช้งานแล้ว กรุณาใช้อีเมลอื่น' 
      }, { status: 400 });
    }

    return NextResponse.json({ 
      error: 'เกิดข้อผิดพลาดในระบบ กรุณาลองใหม่อีกครั้ง',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
    
  } finally {
    await prisma.$disconnect();
  }
}