import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

// POST a new FFQ Question
export async function POST(request: Request) {
  try {
    const { question, foodGroup, order, options = [], type, ffqSetId } = await request.json();

    if (!question || !foodGroup || order === undefined || !type || !ffqSetId) {
      return new NextResponse('Missing required fields', { status: 400 });
    }

    const newQuestion = await prisma.fFQQuestion.create({
      data: {
        question,
        foodGroup,
        order: Number(order),
        options,
        type,
        ffqSetId,
      },
    });

    return NextResponse.json(newQuestion, { status: 201 });
  } catch (error) {
    console.error('Error creating FFQ question:', error);
    return NextResponse.json({ error: 'Failed to create FFQ question' }, { status: 500 });
  }
}
