// src/app/api/session/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getSession, isSessionValid, updateSessionActivity, validateAndSyncSession } from '@/lib/session';

// GET /api/session - ดูข้อมูล session (optimized for speed)
export async function GET(req: NextRequest) {
  try {
    console.log('📊 Session info API called');

    const session = await getSession(req);

    if (!session.isLoggedIn || !session.user) {
      return NextResponse.json({
        error: 'ไม่ได้เข้าสู่ระบบ'
      }, { status: 401 });
    }

    // Check if session is still valid
    if (!isSessionValid(session)) {
      return NextResponse.json({
        error: 'Session หมดอายุ กรุณาเข้าสู่ระบบใหม่'
      }, { status: 401 });
    }

    // Update session activity
    const response = NextResponse.json({
      message: 'ข้อมูล session สำเร็จ',
      currentSession: {
        user: session.user, // Return full user object for compatibility
        loginTime: new Date(session.user.loginTime).toLocaleString('th-TH'),
        lastActivity: new Date(session.user.lastActivity).toLocaleString('th-TH'),
        sessionAge: Math.floor((Date.now() - session.user.loginTime) / 1000 / 60), // minutes
        isActive: true
      },
      loginHistory: session.loginHistory || [],
      sessionBased: true
    }, { status: 200 });

    // Update last activity
    await updateSessionActivity(req, response);

    return response;

  } catch (error: any) {
    console.error('❌ Session info error:', error.message);
    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในการดึงข้อมูล session',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}
