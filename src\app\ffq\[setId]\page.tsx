"use client";

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { 
  C<PERSON>board<PERSON>ist, 
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  Save,
  RotateCcw,
  Clock
} from "lucide-react";

// สมมติว่าเรามี Type เหล่านี้จาก Prisma
interface FFQQuestion {
  id: number;
  question: string;
  foodGroup: string;
  options: any;
  type: string;
}

interface FFQSet {
  id: number;
  name: string;
  description: string | null;
  questions: FFQQuestion[];
}

export default function FFQSubmissionPage() {
  const params = useParams();
  const setId = params.setId;

  const [ffqSet, setFfqSet] = useState<FFQSet | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [answers, setAnswers] = useState<{ [key: number]: any }>({});
  const [submitting, setSubmitting] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);

  const questionsPerPage = 5;

  useEffect(() => {
    if (setId) {
      const fetchFFQSet = async () => {
        try {
          const response = await fetch(`/api/ffq/sets/${setId}`);
          if (!response.ok) {
            throw new Error('ไม่สามารถโหลดข้อมูลชุดคำถามได้');
          }
          const data = await response.json();
          setFfqSet(data);
        } catch (err: any) {
          setError(err.message);
        } finally {
          setLoading(false);
        }
      };

      fetchFFQSet();
    }
  }, [setId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-8 shadow-lg">
          <div className="flex items-center gap-4">
            <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <span className="text-lg font-medium text-gray-700">กำลังโหลด...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-8 shadow-lg max-w-md mx-auto">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <ClipboardList className="w-8 h-8 text-red-600" />
            </div>
            <h2 className="text-xl font-bold text-gray-800 mb-2">เกิดข้อผิดพลาด</h2>
            <p className="text-red-600 mb-4">{error}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="px-6 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors"
            >
              ลองใหม่
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!ffqSet) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-8 shadow-lg max-w-md mx-auto">
          <div className="text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <ClipboardList className="w-8 h-8 text-gray-600" />
            </div>
            <h2 className="text-xl font-bold text-gray-800 mb-2">ไม่พบชุดคำถาม</h2>
            <p className="text-gray-600 mb-4">ไม่พบชุดคำถามที่คุณกำลังมองหา</p>
            <button 
              onClick={() => window.history.back()} 
              className="px-6 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors"
            >
              ย้อนกลับ
            </button>
          </div>
        </div>
      </div>
    );
  }

  const sortedQuestions = ffqSet.questions.sort((a, b) => a.id - b.id);
  const totalPages = Math.ceil(sortedQuestions.length / questionsPerPage);
  const currentQuestions = sortedQuestions.slice(
    currentPage * questionsPerPage,
    (currentPage + 1) * questionsPerPage
  );

  const handleAnswerChange = (questionId: number, value: any) => {
    setAnswers(prev => ({ ...prev, [questionId]: value }));
  };

  const handleNext = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentPage > 0) {
      setCurrentPage(prev => prev - 1);
    }
  };

  const handleReset = () => {
    if (confirm('คุณต้องการลบคำตอบทั้งหมดและเริ่มใหม่หรือไม่?')) {
      setAnswers({});
      setCurrentPage(0);
    }
  };

  const handleSubmit = async () => {
    setSubmitting(true);
    setError(null);

    // สมมติว่ามี userId เป็น 1
    const submissionData = {
      userId: 1, 
      ffqSetId: ffqSet.id,
      answers: Object.keys(answers).map(questionId => ({
        questionId: parseInt(questionId),
        answer: answers[parseInt(questionId)],
      })),
    };

    try {
      const response = await fetch('/api/ffq/submissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(submissionData),
      });

      if (!response.ok) {
        throw new Error('ไม่สามารถส่งคำตอบได้');
      }

      alert('ส่งคำตอบเรียบร้อยแล้ว!');
      window.location.href = '/ffq';
    } catch (err: any) {
      setError(err.message);
    } finally {
      setSubmitting(false);
    }
  };

  const getAnsweredCount = () => {
    return Object.keys(answers).length;
  };

  const getCompletionPercentage = () => {
    return Math.round((getAnsweredCount() / sortedQuestions.length) * 100);
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'ข้อมูลทั่วไป': 'from-blue-400 to-blue-600',
      'ข้าวและแป้ง': 'from-amber-400 to-orange-500',
      'เนื้อสัตว์': 'from-red-400 to-red-600',
      'ปลาและอาหารทะเล': 'from-cyan-400 to-blue-500',
      'ไข่และนม': 'from-yellow-400 to-yellow-600',
      'ผักใบเขียว': 'from-emerald-400 to-green-600',
      'ผลไม้': 'from-pink-400 to-rose-500',
      'เครื่องดื่ม': 'from-purple-400 to-purple-600',
      'ขนมและของหวาน': 'from-indigo-400 to-indigo-600'
    };
    return colors[category] || 'from-gray-400 to-gray-600';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header FFQ */}
      <div className="bg-white/80 backdrop-blur-lg border-b border-gray-200/50 sticky top-0 z-10 w-full">
        <div className="max-w-4xl mx-auto px-4 py-4 md:py-6">
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 md:gap-0">
            <div className="flex items-center gap-4">
              <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                <ClipboardList className="w-5 h-5 md:w-6 md:h-6 text-white" />
              </div>
              <div>
                <h1 className="text-lg md:text-2xl font-bold text-gray-800">{ffqSet.name}</h1>
                <p className="text-xs md:text-base text-gray-600">{ffqSet.description || 'ประเมินพฤติกรรมการรับประทานอาหาร'}</p>
              </div>
            </div>
            <div className="text-left md:text-right mt-2 md:mt-0">
              <div className="text-lg md:text-2xl font-bold text-gray-800">{getAnsweredCount()}/{sortedQuestions.length}</div>
              <div className="text-xs md:text-sm text-gray-500">คำถามที่ตอบแล้ว</div>
            </div>
          </div>
          {/* Progress Bar */}
          <div className="mt-4 md:mt-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs md:text-sm text-gray-600">ความคืบหน้า</span>
              <span className="text-xs md:text-sm font-medium text-gray-800">{getCompletionPercentage()}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 md:h-3">
              <div 
                className="bg-gradient-to-r from-blue-400 to-indigo-500 h-2 md:h-3 rounded-full transition-all duration-500"
                style={{ width: `${getCompletionPercentage()}%` }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-2 sm:px-4 py-8">
        <div className="bg-white/70 backdrop-blur-lg rounded-3xl border border-white/50 shadow-lg">
          {/* Page Header */}
          <div className="p-4 md:p-6 border-b border-gray-200/50 flex flex-col md:flex-row items-start md:items-center justify-between gap-2 md:gap-0">
            <div>
              <h2 className="text-lg md:text-xl font-bold text-gray-800">
                หน้าที่ {currentPage + 1} จาก {totalPages}
              </h2>
              <p className="text-xs md:text-sm text-gray-600">
                คำถามที่ {currentPage * questionsPerPage + 1} - {Math.min((currentPage + 1) * questionsPerPage, sortedQuestions.length)}
              </p>
            </div>
            <div className="flex gap-2 mt-2 md:mt-0">
              <button
                onClick={() => window.history.back()}
                className="flex items-center gap-2 px-3 md:px-4 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-xl transition-colors text-xs md:text-sm border border-gray-200 bg-white"
                title="ย้อนกลับ"
              >
                <ChevronLeft className="w-4 h-4" />
                ย้อนกลับไปหน้า FFQ
              </button>
            
              <button
                onClick={handleReset}
                className="flex items-center gap-2 px-3 md:px-4 py-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-xl transition-colors text-xs md:text-sm"
                title="ลบคำตอบทั้งหมด"
              >
                <RotateCcw className="w-4 h-4" />
                <span>เริ่มใหม่</span>
              </button>
            </div>
          </div>

          {/* Questions */}
          <div className="p-4 md:p-6 space-y-6 md:space-y-8">
            {currentQuestions.map((question, index) => (
              <div key={question.id} className="space-y-3 md:space-y-4 scroll-mt-[120px]">
                {/* Question Header */}
                <div className="flex items-start gap-3 md:gap-4">
                  <div className="flex-shrink-0">
                    <div className={`w-8 h-8 md:w-10 md:h-10 bg-gradient-to-r ${getCategoryColor(question.foodGroup)} rounded-xl flex items-center justify-center shadow-lg`}>
                      <span className="text-white font-bold text-xs md:text-sm">
                        {currentPage * questionsPerPage + index + 1}
                      </span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className={`inline-block px-2 md:px-3 py-1 bg-gradient-to-r ${getCategoryColor(question.foodGroup)} text-white rounded-full text-xs font-medium mb-1 md:mb-2`}>
                      {question.foodGroup}
                    </div>
                    <h3 className="text-base md:text-lg font-semibold text-gray-800 leading-relaxed">
                      {question.question}
                    </h3>
                  </div>
                </div>

                {/* Answer Options */}
                <div className="ml-10 md:ml-14">
                  <RenderQuestionInput 
                    question={question} 
                    value={answers[question.id]} 
                    onChange={handleAnswerChange} 
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Navigation */}
          <div className="p-4 md:p-6 border-t border-gray-200/50">
            <div className="flex flex-col md:flex-row items-center justify-between gap-3 md:gap-0">
              <button
                onClick={handlePrevious}
                disabled={currentPage === 0}
                className={`
                  flex items-center gap-2 px-4 md:px-6 py-2 md:py-3 rounded-xl font-medium transition-all duration-200 text-xs md:text-base
                  ${currentPage === 0
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300 hover:scale-105'
                  }
                `}
              >
                <ChevronLeft className="w-4 h-4" />
                หน้าก่อนหน้า
              </button>

              <div className="flex items-center gap-1 md:gap-2 mt-2 md:mt-0">
                {Array.from({ length: totalPages }, (_, i) => (
                  <button
                    key={i}
                    onClick={() => setCurrentPage(i)}
                    className={`
                      w-8 h-8 md:w-10 md:h-10 rounded-xl font-medium transition-all duration-200 text-xs md:text-base
                      ${i === currentPage
                        ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg'
                        : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                      }
                    `}
                  >
                    {i + 1}
                  </button>
                ))}
              </div>

              {currentPage === totalPages - 1 ? (
                <button
                  onClick={handleSubmit}
                  disabled={submitting || getAnsweredCount() === 0}
                  className={`
                    flex items-center gap-2 px-4 md:px-6 py-2 md:py-3 rounded-xl font-medium transition-all duration-200 text-xs md:text-base
                    ${submitting || getAnsweredCount() === 0
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white hover:from-emerald-600 hover:to-teal-700 hover:scale-105 shadow-lg'
                    }
                  `}
                >
                  {submitting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      กำลังส่ง...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4" />
                      ส่งคำตอบ
                    </>
                  )}
                </button>
              ) : (
                <button
                  onClick={handleNext}
                  className="flex items-center gap-2 px-4 md:px-6 py-2 md:py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl font-medium hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 hover:scale-105 shadow-lg text-xs md:text-base"
                >
                  หน้าถัดไป
                  <ChevronRight className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-4 md:mt-6 bg-white/50 backdrop-blur-lg rounded-2xl p-4 md:p-6 border border-white/30">
          <h3 className="font-bold text-gray-800 mb-2 md:mb-3 flex items-center gap-2 text-base md:text-lg">
            <Clock className="w-5 h-5 text-blue-600" />
            คำแนะนำการตอบแบบสอบถาม
          </h3>
          <div className="space-y-1 md:space-y-2 text-xs md:text-sm text-gray-600">
            <p>• กรุณาตอบตามความเป็นจริงของพฤติกรรมการรับประทานอาหาร</p>
            <p>• หากไม่แน่ใจ ให้เลือกตัวเลือกที่ใกล้เคียงกับความเป็นจริงมากที่สุด</p>
            <p>• สามารถย้อนกลับไปแก้ไขคำตอบได้ก่อนการส่งข้อมูล</p>
            <p>• ข้อมูลจะถูกเก็บเป็นความลับและใช้เพื่อการวิจัยเท่านั้น</p>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mt-4 bg-red-50 border border-red-200 rounded-xl p-4">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}
      </div>
    </div>
  );
}

// Component สำหรับ Render Input ตามประเภทของคำถาม
function RenderQuestionInput({ question, value, onChange }: { question: FFQQuestion, value: any, onChange: (questionId: number, value: any) => void }) {
  const handleFrequencyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newAnswer = { ...value, frequency: e.target.value };
    onChange(question.id, newAnswer);
  };

  const handlePortionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newAnswer = { ...value, portion: e.target.value };
    onChange(question.id, newAnswer);
  };

  const selectClass = "w-full p-3 border-2 border-gray-200 rounded-xl focus:border-blue-400 focus:ring-4 focus:ring-blue-100 transition-all duration-200 bg-white text-gray-700";

  switch (question.type) {
    case 'frequency':
      return (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">ความถี่:</label>
            <select 
              value={value?.frequency || ''}
              onChange={handleFrequencyChange}
              className={selectClass}
            >
              <option value="">-- เลือกความถี่ --</option>
              <option value="daily">ทุกวัน</option>
              <option value="weekly">สัปดาห์ละครั้ง</option>
              <option value="monthly">เดือนละครั้ง</option>
              <option value="rarely">นานๆ ครั้ง</option>
              <option value="never">ไม่เคยเลย</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">ปริมาณ:</label>
            <select 
              value={value?.portion || ''}
              onChange={handlePortionChange}
              className={selectClass}
            >
              <option value="" className="text-black">-- เลือกปริมาณ --</option>
              <option value="small" className="text-black">น้อย</option>
              <option value="medium" className="text-black">ปานกลาง</option>
              <option value="large" className="text-black">มาก</option>
            </select>
          </div>
        </div>
      );
    case 'single':
      return (
        <div className="grid grid-cols-1 gap-2 md:gap-3">
          {(question.options as string[]).map((option, optionIndex) => (
            <label
              key={optionIndex}
              className={`
                flex items-center p-3 md:p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 hover:scale-[1.02]
                ${value === option
                  ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-300 shadow-lg shadow-blue-100'
                  : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-md'
                }
              `}
            >
              <input
                type="radio"
                name={`question-${question.id}`}
                value={option}
                checked={value === option}
                onChange={(e) => onChange(question.id, e.target.value)}
                className="w-4 h-4 md:w-5 md:h-5 text-blue-600 border-gray-300 focus:ring-blue-500 focus:ring-2"
              />
              <span className={`ml-2 md:ml-3 text-xs md:text-sm font-medium text-gray-700`}>
                {option}
              </span>
              {value === option && (
                <CheckCircle className="w-4 h-4 md:w-5 md:h-5 text-blue-600 ml-auto" />
              )}
            </label>
          ))}
        </div>
      );
    default:
      return (
        <input 
          type="text" 
          value={value || ''}
          className="w-full p-3 border-2 border-gray-200 rounded-xl focus:border-blue-400 focus:ring-4 focus:ring-blue-100 transition-all duration-200 bg-white text-gray-700" 
          placeholder="ใส่คำตอบ..." 
          onChange={(e) => onChange(question.id, e.target.value)} 
        />
      );
  }
}