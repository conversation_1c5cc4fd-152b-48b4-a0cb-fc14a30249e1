//src\components\home\DailyOverviewCards.tsx
"use client";

import { Zap, Scale, Target, CheckCircle } from "lucide-react";

interface UserData {
  name: string;
  age: number;
  weight: number;
  height: number;
  bmi: number;
  goal: string;
  dailyCalories: number;
  consumedCalories: number;
  remainingCalories: number;
}

interface DailyOverviewCardsProps {
  userData: UserData;
  isLoadingCalories?: boolean;
}

export default function DailyOverviewCards({ userData, isLoadingCalories }: DailyOverviewCardsProps) {
  
  // กำหนดข้อความเป้าหมาย
  const getGoalText = (goal: string) => {
    switch (goal) {
      case "lose_weight": return "ลดน้ำหนัก";
      case "gain_weight": return "เพิ่มน้ำหนัก";
      case "maintain": return "รักษาน้ำหนัก";
      case "health": return "สุขภาพดีขึ้น";
      default: return "สุขภาพดีขึ้น";
    }
  };

  // กำหนดสถานะ BMI
  const getBMIStatus = (bmi: number) => {
    if (bmi < 18.5) return "น้ำหนักต่ำ";
    if (bmi < 25) return "น้ำหนักปกติ";
    if (bmi < 30) return "น้ำหนักเกิน";
    return "อ้วน";
  };

  // กำหนดสี BMI
  const getBMIColor = (bmi: number) => {
    if (bmi < 18.5) return "text-blue-600";
    if (bmi < 25) return "text-emerald-600";
    if (bmi < 30) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Calories Card */}
      <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-6 border border-white/50 shadow-lg">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-xl flex items-center justify-center">
            <Zap className="w-6 h-6 text-white" />
          </div>
          <span className="text-sm text-gray-500">วันนี้</span>
        </div>
        
        {isLoadingCalories ? (
          <>
            <div className="flex items-center gap-2 mb-1">
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-emerald-500 border-t-transparent"></div>
              <span className="text-lg font-bold text-gray-400">กำลังโหลด...</span>
            </div>
            <p className="text-sm text-gray-400 mb-3">
              จาก {userData.dailyCalories.toLocaleString()} แคลอรี่
            </p>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-gray-300 h-2 rounded-full w-0 transition-all duration-500" />
            </div>
          </>
        ) : (
          <>
            <h3 className="text-2xl font-bold text-gray-800 mb-1">
              {userData.consumedCalories.toLocaleString()}
            </h3>
            <p className="text-sm text-gray-600 mb-3">
              จาก {userData.dailyCalories.toLocaleString()} แคลอรี่
            </p>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-emerald-400 to-teal-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${Math.min(100, (userData.consumedCalories / userData.dailyCalories) * 100)}%` }}
              />
            </div>
          </>
        )}
      </div>

      {/* BMI Card */}
      <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-6 border border-white/50 shadow-lg">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-xl flex items-center justify-center">
            <Scale className="w-6 h-6 text-white" />
          </div>
          <span className="text-sm text-gray-500">BMI</span>
        </div>
        <h3 className="text-2xl font-bold text-gray-800 mb-1">
          {userData.bmi.toFixed(1)}
        </h3>
        <p className={`text-sm font-medium ${getBMIColor(userData.bmi)}`}>
          {getBMIStatus(userData.bmi)}
        </p>
        <p className="text-xs text-gray-500 mt-1">
          {userData.weight} กก. / {userData.height} ซม.
        </p>
      </div>

      {/* Goal Card */}
      <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-6 border border-white/50 shadow-lg">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-xl flex items-center justify-center">
            <Target className="w-6 h-6 text-white" />
          </div>
          <span className="text-sm text-gray-500">เป้าหมาย</span>
        </div>
        <h3 className="text-lg font-bold text-gray-800 mb-1">
          {getGoalText(userData.goal)}
        </h3>
        <p className="text-sm text-gray-600">
          กำลังดำเนินการ
        </p>
        <div className="flex items-center mt-2">
          <CheckCircle className="w-4 h-4 text-emerald-500 mr-2" />
          <span className="text-xs text-emerald-600">อยู่ในเป้า</span>
        </div>
      </div>
    </div>
  );
}