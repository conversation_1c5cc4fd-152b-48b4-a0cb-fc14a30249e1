// AdminUsers.tsx
"use client";

import { 
  Search, 
  Filter, 
  Plus, 
  Edit3, 
  Trash2, 
  <PERSON>, 
  CheckCircle, 
  XCircle, 
  Clock 
} from "lucide-react";

// Add type for user
interface User {
  id: number;
  name: string;
  email: string;
  status: 'active' | 'pending' | 'inactive';
  lastLogin: string;
  meals: number;
  completion: number;
}

export default function AdminUsers() {
  const users: User[] = [
    { id: 1, name: 'สมชาย ใจดี', email: '<EMAIL>', status: 'active', lastLogin: '2024-06-22 09:30', meals: 24, completion: 85 },
    { id: 2, name: 'สุดา วงศ์วิทย์', email: '<EMAIL>', status: 'pending', lastLogin: '2024-06-21 14:20', meals: 12, completion: 65 },
    { id: 3, name: 'ประยุทธ์ สีเขียว', email: '<EMAIL>', status: 'active', lastLogin: '2024-06-22 11:15', meals: 31, completion: 92 },
    { id: 4, name: 'มาลี แสงทอง', email: '<EMAIL>', status: 'inactive', lastLogin: '2024-06-19 16:45', meals: 8, completion: 45 },
    { id: 5, name: 'วิชัย โชคดี', email: '<EMAIL>', status: 'active', lastLogin: '2024-06-22 10:20', meals: 18, completion: 78 }
  ];

  // Add type for UserRow props
  const UserRow = ({ user }: { user: User }) => (
    <tr className="hover:bg-gray-50/80 transition-colors">
      <td className="px-6 py-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-teal-600 rounded-full flex items-center justify-center">
            <span className="text-white font-medium text-sm">{user.name.charAt(0)}</span>
          </div>
          <div>
            <div className="font-medium text-gray-900">{user.name}</div>
            <div className="text-sm text-gray-500">{user.email}</div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4">
        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
          user.status === 'active' ? 'bg-emerald-100 text-emerald-700' :
          user.status === 'pending' ? 'bg-yellow-100 text-yellow-700' :
          'bg-gray-100 text-gray-700'
        }`}>
          {user.status === 'active' ? <CheckCircle className="w-3 h-3" /> :
           user.status === 'pending' ? <Clock className="w-3 h-3" /> :
           <XCircle className="w-3 h-3" />}
          {user.status === 'active' ? 'ใช้งาน' : 
           user.status === 'pending' ? 'รอยืนยัน' : 'ไม่ใช้งาน'}
        </span>
      </td>
      <td className="px-6 py-4 text-sm text-gray-900">{user.lastLogin}</td>
      <td className="px-6 py-4 text-sm text-gray-900">{user.meals} มื้อ</td>
      <td className="px-6 py-4">
        <div className="flex items-center gap-2">
          <div className="w-20 bg-gray-200 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-emerald-400 to-teal-500 h-2 rounded-full" 
              style={{ width: `${user.completion}%` }}
            />
          </div>
          <span className="text-sm font-medium text-gray-700">{user.completion}%</span>
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="flex items-center gap-1">
          <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors" title="ดูรายละเอียด">
            <Eye className="w-4 h-4 text-gray-600" />
          </button>
          <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors" title="แก้ไข">
            <Edit3 className="w-4 h-4 text-gray-600" />
          </button>
          <button className="p-2 hover:bg-red-50 rounded-lg transition-colors" title="ลบ">
            <Trash2 className="w-4 h-4 text-red-500" />
          </button>
        </div>
      </td>
    </tr>
  );

  return (
    <div className="space-y-6">
      <div className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 border border-white/50 shadow-lg">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          <h3 className="text-lg font-bold text-gray-800">จัดการผู้ใช้งาน</h3>
          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
              <input
                type="text"
                placeholder="ค้นหาผู้ใช้..."
                className="pl-10 pr-4 py-2 border border-gray-200 rounded-xl focus:border-emerald-400 focus:ring-2 focus:ring-emerald-100 transition-colors"
              />
            </div>
            <button className="flex items-center gap-2 px-4 py-2 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors">
              <Filter className="w-4 h-4" />
              กรอง
            </button>
            <button className="flex items-center gap-2 px-4 py-2 bg-emerald-500 text-white rounded-xl hover:bg-emerald-600 transition-colors">
              <Plus className="w-4 h-4" />
              เพิ่มผู้ใช้
            </button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-6 font-semibold text-gray-700">ผู้ใช้</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-700">สถานะ</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-700">เข้าใช้ล่าสุด</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-700">จำนวนมื้อ</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-700">ความครบถ้วน</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-700">การจัดการ</th>
              </tr>
            </thead>
            <tbody>
              {users.map(user => (
                <UserRow key={user.id} user={user} />
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
          <div className="text-sm text-gray-600">
            แสดง 1-5 จาก 1,247 รายการ
          </div>
          <div className="flex items-center gap-2">
            <button className="px-3 py-1 border border-gray-200 rounded hover:bg-gray-50 transition-colors">ก่อนหน้า</button>
            <button className="px-3 py-1 bg-emerald-500 text-white rounded">1</button>
            <button className="px-3 py-1 border border-gray-200 rounded hover:bg-gray-50 transition-colors">2</button>
            <button className="px-3 py-1 border border-gray-200 rounded hover:bg-gray-50 transition-colors">3</button>
            <button className="px-3 py-1 border border-gray-200 rounded hover:bg-gray-50 transition-colors">ถัดไป</button>
          </div>
        </div>
      </div>
    </div>
  );
}