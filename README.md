# 🍎 Project Nutrition - ระบบบันทึกโภชนาการ

ระบบบันทึกและติดตามโภชนาการที่ทันสมัย พัฒนาด้วย Next.js 15, TypeScript, และ Prisma

## ✨ Features

- 🔐 **ระบบ Authentication ที่ปลอดภัย**
  - Session-based authentication ด้วย Iron Session
  - JWT token support สำหรับ backward compatibility
  - Rate limiting ป้องกัน brute force attacks
  - Remember me functionality พร้อม autofill
  - Logout confirmation modal

- 📊 **การจัดการข้อมูลผู้ใช้**
  - ข้อมูลส่วนตัวและเป้าหมายสุขภาพ
  - คำนวณ B<PERSON>, B<PERSON>, TDEE อัตโนมัติ
  - ประวัติการเข้าสู่ระบบ

- 🛡️ **ความปลอดภัย**
  - Rate limiting แบบ intelligent (ไม่นับ successful attempts)
  - Password hashing ด้วย bcrypt
  - Session encryption
  - CORS protection

- 🎨 **UI/UX ที่สวยงาม**
  - Responsive design ด้วย Tailwind CSS
  - Modern glassmorphism design
  - Loading states และ animations
  - Icons จาก Lucide React

## 🚀 Getting Started

### Prerequisites

- Node.js 18.18.0 หรือใหม่กว่า
- npm, yarn, pnpm หรือ bun
- PostgreSQL database (แนะนำ Neon DB)

### Installation

1. **Clone repository**
```bash
git clone <repository-url>
cd project-nutrition
```

2. **ติดตั้ง dependencies**
```bash
npm install
# หรือ
yarn install
# หรือ
pnpm install
```

3. **ตั้งค่า Environment Variables**
```bash
cp .env.example .env
```
แก้ไขไฟล์ `.env` ตามความต้องการ (ดูรายละเอียดใน [Environment Variables](#environment-variables))

4. **ตั้งค่า Database**
```bash
# Generate Prisma client
npx prisma generate

# Run database migrations
npx prisma db push

# (Optional) Seed database
npx prisma db seed
```

5. **รัน Development Server**
```bash
npm run dev
```

เปิด [http://localhost:3000](http://localhost:3000) ในเบราว์เซอร์

## 📦 Tech Stack

### Core Framework
- **Next.js 15.3.3** - React framework with App Router
- **React 19** - UI library
- **TypeScript 5** - Type safety

### Database & ORM
- **Prisma 6.8.2** - Database ORM
- **PostgreSQL** - Primary database

### Authentication & Security
- **Iron Session 8.0.4** - Secure session management
- **bcrypt 6.0.0** - Password hashing
- **jsonwebtoken 9.0.2** - JWT tokens
- **Custom Rate Limiting** - In-memory rate limiting

### UI & Styling
- **Tailwind CSS 4** - Utility-first CSS framework
- **Lucide React 0.511.0** - Beautiful icons
- **Framer Motion 12.15.0** - Animations (optional)

### Development Tools
- **ESLint 9** - Code linting
- **TypeScript** - Type checking
- **Prisma Studio** - Database GUI

## 🔧 Environment Variables

สร้างไฟล์ `.env` ในโฟลเดอร์ root:

```env
# Database Configuration
DATABASE_URL="your_postgresql_connection_string"

# JWT Configuration
JWT_SECRET="your_jwt_secret_key_at_least_32_characters"
JWT_EXPIRES_IN="7d"

# Session Configuration (Iron Session)
SESSION_SECRET="your_session_secret_at_least_32_characters"
SESSION_MAX_AGE="2592000000"  # 30 days in milliseconds

# Encryption for Autofill
ENCRYPTION_KEY="your_encryption_key_32_characters"

# Environment
NODE_ENV="development"

# Frontend URL
FRONTEND_URL="http://localhost:3000"

# Rate Limiting
RATE_LIMIT_MAX="100"
RATE_LIMIT_WINDOW="15"
```

### Required Variables
- `DATABASE_URL` - PostgreSQL connection string
- `SESSION_SECRET` - Secret key สำหรับ Iron Session (อย่างน้อย 32 ตัวอักษร)

### Optional Variables
- `JWT_SECRET` - สำหรับ JWT tokens (backward compatibility)
- `ENCRYPTION_KEY` - สำหรับ encrypt autofill passwords
- `FRONTEND_URL` - สำหรับ password reset links
- `RATE_LIMIT_*` - การตั้งค่า rate limiting

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   │   ├── auth/          # Authentication endpoints
│   │   ├── login/         # Login/logout endpoints
│   │   ├── register/      # Registration endpoint
│   │   ├── session/       # Session management
│   │   └── autofill/      # Autofill credentials
│   ├── login/             # Login page
│   ├── register/          # Registration page
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   └── LogoutConfirmModal.tsx
├── lib/                   # Utility libraries
│   ├── auth-utils.ts      # Authentication utilities
│   ├── rate-limit.ts      # Rate limiting system
│   └── session.ts         # Session management
├── middleware.ts          # Next.js middleware
└── types/                 # TypeScript type definitions
```

## 🔐 Authentication System

### Session-based Authentication
- ใช้ Iron Session สำหรับความปลอดภัยสูง
- Session cookies มี httpOnly และ secure flags
- Auto-expire sessions หลังจาก 30 วัน

### Rate Limiting
- **Intelligent Rate Limiting**: ไม่นับ successful login attempts
- **Login**: 5 attempts ต่อ 15 นาที
- **Registration**: 3 attempts ต่อ 1 ชั่วโมง
- **Password Reset**: 3 attempts ต่อ 1 ชั่วโมง

### Remember Me Feature
- Autofill credentials บนหน้า login
- สามารถลบ saved credentials เมื่อ uncheck remember me
- Encrypted password storage ใน session

## 🛠️ Available Scripts

```bash
# Development
npm run dev          # Start development server

# Building
npm run build        # Build for production
npm run start        # Start production server

# Database
npx prisma generate  # Generate Prisma client
npx prisma db push   # Push schema to database
npx prisma studio    # Open Prisma Studio

# Code Quality
npm run lint         # Run ESLint
```

## 🚀 Deployment

### Vercel (Recommended)
1. Push code ไป GitHub
2. Connect repository ใน Vercel
3. ตั้งค่า Environment Variables
4. Deploy!

### Environment Variables สำหรับ Production
```env
NODE_ENV="production"
DATABASE_URL="your_production_database_url"
SESSION_SECRET="your_production_session_secret"
FRONTEND_URL="https://your-domain.com"
```

## 🤝 Contributing

1. Fork the project
2. Create feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open Pull Request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

หากมีปัญหาหรือข้อสงสัย:
1. ตรวจสอบ [Issues](../../issues) ที่มีอยู่
2. สร้าง Issue ใหม่พร้อมรายละเอียด
3. ติดต่อทีมพัฒนา

---

Made with ❤️ by [Your Team Name]
