// AdminHeader.tsx
"use client";

import React, { useState, useEffect, useRef } from "react";
import { Bell, Utensils, Menu, X, ChevronDown, User, Settings, Target, Book } from "lucide-react";

interface AdminHeaderProps {
  isSidebarOpen: boolean;
  onToggleSidebar: () => void;
  userName?: string;
  onLogoutClick?: () => void;
}

export default function AdminHeader({ isSidebarOpen, onToggleSidebar, userName = "Admin", onLogoutClick = () => {} }: AdminHeaderProps) {
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const profileRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (profileRef.current && !profileRef.current.contains(event.target as Node)) {
        setIsProfileOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <header className="bg-white/80 backdrop-blur-lg border-b border-gray-200/50 sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Title */}
          <div className="flex items-center space-x-4">
            <button 
              onClick={onToggleSidebar}
              className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              {isSidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                <Utensils className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  Admin Dashboard
                </h1>
                <div className="text-xs text-gray-600">ระบบจัดการโภชนาการ</div>
              </div>
            </div>
          </div>

          {/* Right Section */}
          <div className="flex items-center space-x-3">
            <button className="p-2 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors relative">
              <Bell className="w-5 h-5 text-gray-600" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-white"></span>
            </button>
            {/* Profile Dropdown */}
            <div className="relative" ref={profileRef}>
              <button
                onClick={() => setIsProfileOpen((v) => !v)}
                className="flex items-center space-x-2 px-3 py-2 bg-gray-50 rounded-lg border border-gray-200/50 hover:bg-gray-100 transition-colors"
              >
                <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-teal-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-medium text-sm">{userName.charAt(0)}</span>
                </div>
                <span className="text-sm font-medium text-gray-700 hidden sm:block max-w-24 truncate">{userName}</span>
                <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${isProfileOpen ? 'rotate-180' : ''}`} />
              </button>
              {/* Dropdown */}
              <div className={`
                absolute right-0 mt-2 w-64 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 
                transition-all duration-200 origin-top-right z-50
                ${isProfileOpen 
                  ? 'opacity-100 scale-100 translate-y-0' 
                  : 'opacity-0 scale-95 -translate-y-2 pointer-events-none'
                }
              `}>
                <div className="p-4">
                  {/* User Info */}
                  <div className="flex items-center space-x-3 pb-4 border-b border-gray-200/50">
                    <div className="w-12 h-12 bg-gradient-to-br from-emerald-400 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                      <User className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-semibold text-gray-900 truncate">{userName}</p>
                      <p className="text-xs text-gray-600">ผู้ดูแลระบบ</p>
                    </div>
                  </div>
                  {/* Menu Items */}
                  <div className="py-2 space-y-1">
                    <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100/80 rounded-lg transition-colors flex items-center space-x-3">
                      <User className="w-4 h-4" />
                      <span>ข้อมูลส่วนตัว</span>
                    </button>
                    <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100/80 rounded-lg transition-colors flex items-center space-x-3">
                      <Target className="w-4 h-4" />
                      <span>เป้าหมาย</span>
                    </button>
                    <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100/80 rounded-lg transition-colors flex items-center space-x-3">
                      <Book className="w-4 h-4" />
                      <span>คู่มือการใช้งาน</span>
                    </button>
                    <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100/80 rounded-lg transition-colors flex items-center space-x-3">
                      <Settings className="w-4 h-4" />
                      <span>การตั้งค่า</span>
                    </button>
                  </div>
                  {/* Logout */}
                  <div className="pt-2 border-t border-gray-200/50">
                    <button
                      onClick={() => {
                        onLogoutClick();
                        setIsProfileOpen(false);
                      }}
                      className="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50/80 rounded-lg transition-colors"
                    >
                      ออกจากระบบ
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}