//src\app\api\foods\route.ts
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET: Return all foods from the database
export async function GET(req: NextRequest) {
  try {
    const foods = await prisma.food.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' },
    });
    return NextResponse.json({ foods });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Failed to fetch foods' }, { status: 500 });
  }
}
