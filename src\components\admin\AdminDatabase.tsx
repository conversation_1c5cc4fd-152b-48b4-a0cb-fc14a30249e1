// AdminDatabase.tsx
"use client";

import { Apple, Plus, Edit3, Eye } from "lucide-react";

export default function AdminDatabase() {
  const foodCategories = [
    { 
      id: 1, 
      name: 'นม', 
      items: 15, 
      calories: '150-200 kcal/หน่วย', 
      color: 'bg-blue-100 text-blue-700',
      examples: ['นมสด', 'นมข้นหวาน', 'โยเกิร์ต']
    },
    { 
      id: 2, 
      name: 'เนื้อสัตว์', 
      items: 45, 
      calories: '75-100 kcal/หน่วย', 
      color: 'bg-red-100 text-red-700',
      examples: ['เนื้อหมู', 'เนื้อไก่', 'ปลา', 'ไข่']
    },
    { 
      id: 3, 
      name: 'แป้ง', 
      items: 35, 
      calories: '80-120 kcal/หน่วย', 
      color: 'bg-yellow-100 text-yellow-700',
      examples: ['ข้าวสวย', 'ขนมปัง', 'บะหมี่']
    },
    { 
      id: 4, 
      name: 'ผัก', 
      items: 28, 
      calories: '25 kcal/หน่วย', 
      color: 'bg-green-100 text-green-700',
      examples: ['ผักบุ้ง', 'กะหล่ำปลี', 'มะเขือเทศ']
    },
    { 
      id: 5, 
      name: 'ผลไม้', 
      items: 22, 
      calories: '60 kcal/หน่วย', 
      color: 'bg-orange-100 text-orange-700',
      examples: ['กล้วย', 'แอปเปิ้ล', 'ส้ม']
    },
    { 
      id: 6, 
      name: 'ไขมัน', 
      items: 18, 
      calories: '45 kcal/หน่วย', 
      color: 'bg-purple-100 text-purple-700',
      examples: ['น้ำมันพืช', 'เนย', 'ถั่วลิสง']
    }
  ];

  return (
    <div className="space-y-6">
      <div className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 border border-white/50 shadow-lg">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          <div>
            <h3 className="text-lg font-bold text-gray-800">ฐานข้อมูลอาหาร - 6 หมวดหมู่</h3>
            <p className="text-sm text-gray-600 mt-1">จัดการรายการอาหารตามหลักการแลกเปลี่ยนอาหาร</p>
          </div>
          <button className="flex items-center gap-2 px-4 py-2 bg-emerald-500 text-white rounded-xl hover:bg-emerald-600 transition-colors">
            <Plus className="w-4 h-4" />
            เพิ่มรายการอาหาร
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {foodCategories.map(category => (
            <div key={category.id} className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300">
              <div className={`${category.color} p-4`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Apple className="w-6 h-6" />
                    <h4 className="font-bold text-lg">{category.name}</h4>
                  </div>
                  <span className="px-2 py-1 bg-white/80 rounded-full text-sm font-medium">
                    {category.items} รายการ
                  </span>
                </div>
              </div>
              
              <div className="p-4">
                <div className="mb-3">
                  <div className="text-xs text-gray-500 mb-1">ค่าพลังงาน</div>
                  <div className="text-sm font-medium text-gray-800">{category.calories}</div>
                </div>
                
                <div className="mb-4">
                  <div className="text-xs text-gray-500 mb-2">ตัวอย่างอาหาร</div>
                  <div className="flex flex-wrap gap-1">
                    {category.examples.map((example, index) => (
                      <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                        {example}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <button className="flex-1 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium transition-colors flex items-center justify-center gap-2">
                    <Eye className="w-4 h-4" />
                    ดูรายการ
                  </button>
                  <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors" title="แก้ไข">
                    <Edit3 className="w-4 h-4 text-gray-600" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-emerald-600">163</div>
              <div className="text-sm text-gray-600">รายการทั้งหมด</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">6</div>
              <div className="text-sm text-gray-600">หมวดหมู่</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">12</div>
              <div className="text-sm text-gray-600">เพิ่มใหม่สัปดาห์นี้</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">95%</div>
              <div className="text-sm text-gray-600">ความครบถ้วน</div>
            </div>
          </div>
        </div>
      </div>

      {/* Food Exchange Guidelines */}
      <div className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 border border-white/50 shadow-lg">
        <h3 className="text-lg font-bold text-gray-800 mb-4">หลักการแลกเปลี่ยนอาหาร</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-semibold text-yellow-800 mb-2">แป้ง (1 ส่วน = 80 kcal)</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• ข้าวเหนียว 1/2 ทัพพี</li>
              <li>• ขนมปัง 1 แผ่น</li>
              <li>• บะหมี่แห้ง 1/2 ทัพพี</li>
            </ul>
          </div>
          
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 className="font-semibold text-red-800 mb-2">เนื้อสัตว์ (1 ส่วน = 75 kcal)</h4>
            <ul className="text-sm text-red-700 space-y-1">
              <li>• เนื้อสัตว์ 2 ช้อนโต๊ะ</li>
              <li>• ไข่ไก่ 1 ฟอง</li>
              <li>• ปลา 2 ช้อนโต๊ะ</li>
            </ul>
          </div>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-semibold text-green-800 mb-2">ผัก (1 ส่วน = 25 kcal)</h4>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• ผักสด 1 ถ้วยตวง</li>
              <li>• ผักต้ม 1/2 ถ้วยตวง</li>
              <li>• น้ำผักผลไม้ 1 แก้ว</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}