"use client";

import { useState } from "react";
import { 
  User, Mail, Lock, Calendar, Scale, Ruler, Activity, 
  Target, Heart, ArrowRight, ArrowLeft, Check, AlertCircle, 
  Utensils, Shield, BookOpen, ChevronDown
} from "lucide-react";

export default function ModernNutritionRegister() {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // ข้อมูลบัญชี
    fullName: "",
    email: "",
    password: "",
    confirmPassword: "",
    
    // ข้อมูลร่างกาย
    gender: "",
    birthDate: "",
    height: "",
    weight: "",
    activityLevel: "moderate",
    
    // เป้าหมาย
    goal: "",
    foodAllergies: ""
  });
  
  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [focusedField, setFocusedField] = useState("");

  const handleChange = (e: { target: { name: any; value: any; }; }) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const nextStep = () => setCurrentStep(currentStep + 1);
  const prevStep = () => setCurrentStep(currentStep - 1);

  const handleSubmit = async (e: { preventDefault: () => void; }) => {
    e.preventDefault();
    setMessage("");
    setLoading(true);

    try {
      const res = await fetch("/api/register", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: 'include', // Include cookies for auto login
        body: JSON.stringify({
          ...formData,
          height: parseFloat(formData.height),
          weight: parseFloat(formData.weight),
          birthDate: new Date(formData.birthDate).toISOString(),
        }),
      });

      const data = await res.json();
      setLoading(false);

      if (res.ok) {
        setMessage("ลงทะเบียนสำเร็จ! กำลังเข้าสู่ระบบ...");

        // Check if auto login was successful
        if (data.autoLogin && data.sessionBased) {
          console.log('✅ Auto login successful, redirecting to dashboard');

          // Store user data temporarily for smooth transition
          if (data.user) {
            sessionStorage.setItem('tempUserData', JSON.stringify(data.user));
          }

          // Redirect to main page after short delay
          setTimeout(() => {
            window.location.href = '/';
          }, 1500);
        } else {
          // Fallback: redirect to login page
          setTimeout(() => {
            window.location.href = '/login';
          }, 2000);
        }
      } else {
        setMessage(data.error || "เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง");
      }
    } catch (error) {
      setLoading(false);
      setMessage("เกิดข้อผิดพลาดในการเชื่อมต่อ กรุณาลองใหม่อีกครั้ง");
    }
  };

  // ตัวเลือกต่างๆ
  const activityOptions = [
    { 
      value: "sedentary", 
      label: "นั่งทำงาน",
      detail: "ไม่ค่อยได้ออกกำลังกาย",
      icon: "💺"
    },
    { 
      value: "light", 
      label: "ออกกำลังกายเบา",
      detail: "1-2 วัน/สัปดาห์",
      icon: "🚶"
    },
    { 
      value: "moderate", 
      label: "ออกกำลังกายปานกลาง",
      detail: "3-5 วัน/สัปดาห์",
      icon: "🏃"
    },
    { 
      value: "active", 
      label: "ออกกำลังกายหนัก",
      detail: "6-7 วัน/สัปดาห์",
      icon: "🏋️"
    },
    { 
      value: "very_active", 
      label: "ออกกำลังกายหนักมาก",
      detail: "ฝึกซ้อมเป็นประจำ",
      icon: "🏆"
    }
  ];

  const goalOptions = [
    { 
      value: "weight_loss", 
      label: "ลดน้ำหนัก", 
      icon: "📉",
      color: "from-red-400 to-pink-500",
      bg: "bg-red-50 hover:bg-red-100"
    },
    { 
      value: "maintain", 
      label: "รักษาน้ำหนัก", 
      icon: "⚖️",
      color: "from-blue-400 to-cyan-500",
      bg: "bg-blue-50 hover:bg-blue-100"
    },
    { 
      value: "weight_gain", 
      label: "เพิ่มน้ำหนัก", 
      icon: "📈",
      color: "from-green-400 to-emerald-500",
      bg: "bg-green-50 hover:bg-green-100"
    },
    { 
      value: "muscle_gain", 
      label: "เพิ่มกล้ามเนื้อ", 
      icon: "💪",
      color: "from-purple-400 to-violet-500",
      bg: "bg-purple-50 hover:bg-purple-100"
    },
    { 
      value: "health", 
      label: "สุขภาพดีขึ้น", 
      icon: "❤️",
      color: "from-orange-400 to-amber-500",
      bg: "bg-orange-50 hover:bg-orange-100"
    }
  ];

  // Progress indicator
  const renderProgress = () => {
    const steps = [
      { icon: User, label: "ข้อมูลส่วนตัว", step: 1 },
      { icon: Activity, label: "ข้อมูลร่างกาย", step: 2 },
      { icon: Target, label: "เป้าหมาย", step: 3 }
    ];

    return (
      <div className="mb-12">
        <div className="flex items-center justify-between relative">
          {/* Progress Line */}
          <div className="absolute top-8 left-0 right-0 h-1 bg-gray-200 rounded-full">
            <div 
              className="h-full bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full transition-all duration-700 ease-out"
              style={{ width: `${((currentStep - 1) / 2) * 100}%` }}
            />
          </div>
          
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isActive = currentStep >= step.step;
            const isCompleted = currentStep > step.step;
            
            return (
              <div key={step.step} className="relative flex flex-col items-center z-10">
                <div className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-500 shadow-lg ${
                  isCompleted 
                    ? 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white scale-110'
                    : isActive 
                      ? 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white scale-105'
                      : 'bg-white text-gray-400 border-2 border-gray-200'
                }`}>
                  {isCompleted ? <Check className="w-7 h-7" /> : <Icon className="w-7 h-7" />}
                </div>
                <span className={`mt-3 text-sm font-semibold transition-colors ${
                  isActive ? 'text-emerald-600' : 'text-gray-500'
                }`}>
                  {step.label}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8 px-4 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 rounded-full bg-gradient-to-br from-blue-200/30 to-indigo-300/30 blur-3xl" />
        <div className="absolute top-1/2 -left-40 w-96 h-96 rounded-full bg-gradient-to-br from-emerald-200/30 to-teal-300/30 blur-3xl" />
        <div className="absolute -bottom-40 right-1/3 w-80 h-80 rounded-full bg-gradient-to-br from-purple-200/30 to-pink-300/30 blur-3xl" />
      </div>
      
      <div className="relative z-10 max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-3xl mb-8 shadow-2xl">
            <Utensils className="w-12 h-12 text-white" />
          </div>
          <h1 className="text-5xl font-bold bg-gradient-to-r from-emerald-600 via-teal-600 to-blue-600 bg-clip-text text-transparent mb-6">
            ระบบบันทึกโภชนาการ
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
            เริ่มต้นการเดินทางสู่สุขภาพที่ดีกับระบบวิเคราะห์โภชนาการที่ทันสมัย
          </p>
          <div className="flex items-center justify-center gap-8 text-sm text-gray-500">
            <div className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-emerald-500" />
              <span>ข้อมูลปลอดภัย</span>
            </div>
            <div className="flex items-center gap-2">
              <BookOpen className="w-5 h-5 text-emerald-500" />
              <span>วิเคราะห์แบบละเอียด</span>
            </div>
            <div className="flex items-center gap-2">
              <Heart className="w-5 h-5 text-emerald-500" />
              <span>เป้าหมายเฉพาะบุคคล</span>
            </div>
          </div>
        </div>
        
        {/* Main Form Card */}
        <div className="bg-white/70 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/50 overflow-hidden">
          <div className="p-10">
            {renderProgress()}
            
            {/* Step 1: Personal Information */}
            {currentStep === 1 && (
              <div className="space-y-8">
                <div className="text-center mb-10">
                  <h2 className="text-3xl font-bold text-gray-800 mb-3">ข้อมูลส่วนตัว</h2>
                  <p className="text-gray-600">กรอกข้อมูลบัญชีและข้อมูลส่วนตัวของคุณ</p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      ชื่อ-นามสกุล <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <User className={`absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 transition-colors ${
                        focusedField === 'fullName' ? 'text-emerald-500' : 'text-gray-400'
                      }`} />
                      <input
                        type="text"
                        name="fullName"
                        value={formData.fullName}
                        onChange={handleChange}
                        onFocus={() => setFocusedField('fullName')}
                        onBlur={() => setFocusedField('')}
                        className={`pl-12 w-full py-4 border-2 rounded-2xl transition-all duration-300 text-black placeholder-gray-400 ${
                          focusedField === 'fullName' 
                            ? 'border-emerald-400 ring-4 ring-emerald-100 bg-white' 
                            : 'border-gray-200 bg-gray-50/50 hover:border-gray-300'
                        }`}
                        placeholder="กรอกชื่อและนามสกุล"
                        required
                      />
                      {formData.fullName && (
                        <Check className="absolute right-4 top-1/2 -translate-y-1/2 w-5 h-5 text-emerald-500" />
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      อีเมล <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <Mail className={`absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 transition-colors ${
                        focusedField === 'email' ? 'text-emerald-500' : 'text-gray-400'
                      }`} />
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        onFocus={() => setFocusedField('email')}
                        onBlur={() => setFocusedField('')}
                        className={`pl-12 w-full py-4 border-2 rounded-2xl transition-all duration-300 text-black placeholder-gray-400 ${
                          focusedField === 'email' 
                            ? 'border-emerald-400 ring-4 ring-emerald-100 bg-white' 
                            : 'border-gray-200 bg-gray-50/50 hover:border-gray-300'
                        }`}
                        placeholder="<EMAIL>"
                        required
                      />
                      {formData.email && (
                        <Check className="absolute right-4 top-1/2 -translate-y-1/2 w-5 h-5 text-emerald-500" />
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      เพศ <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <User className={`absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 transition-colors ${
                        focusedField === 'gender' ? 'text-emerald-500' : 'text-gray-400'
                      }`} />
                      <select
                        name="gender"
                        value={formData.gender}
                        onChange={handleChange}
                        onFocus={() => setFocusedField('gender')}
                        onBlur={() => setFocusedField('')}
                        className={`pl-12 pr-4 w-full py-4 border-2 rounded-2xl appearance-none transition-all duration-300 text-black bg-white ${
                          focusedField === 'gender' 
                            ? 'border-emerald-400 ring-4 ring-emerald-100' 
                            : 'border-gray-200 bg-gray-50/50 hover:border-gray-300'
                        }`}
                        required
                      >
                        <option value="">เลือกเพศ</option>
                        <option value="male">ชาย</option>
                        <option value="female">หญิง</option>
                        <option value="other">อื่นๆ</option>
                      </select>
                      <ChevronDown className="absolute right-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      รหัสผ่าน <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <Lock className={`absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 transition-colors ${
                        focusedField === 'password' ? 'text-emerald-500' : 'text-gray-400'
                      }`} />
                      <input
                        type="password"
                        name="password"
                        value={formData.password}
                        onChange={handleChange}
                        onFocus={() => setFocusedField('password')}
                        onBlur={() => setFocusedField('')}
                        className={`pl-12 w-full py-4 border-2 rounded-2xl transition-all duration-300 text-black placeholder-gray-400 ${
                          focusedField === 'password' 
                            ? 'border-emerald-400 ring-4 ring-emerald-100 bg-white' 
                            : 'border-gray-200 bg-gray-50/50 hover:border-gray-300'
                        }`}
                        placeholder="อย่างน้อย 8 ตัวอักษร"
                        minLength={8}
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      ยืนยันรหัสผ่าน <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <Lock className={`absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 transition-colors ${
                        focusedField === 'confirmPassword' ? 'text-emerald-500' : 'text-gray-400'
                      }`} />
                      <input
                        type="password"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleChange}
                        onFocus={() => setFocusedField('confirmPassword')}
                        onBlur={() => setFocusedField('')}
                        className={`pl-12 w-full py-4 border-2 rounded-2xl transition-all duration-300 text-black placeholder-gray-400 ${
                          focusedField === 'confirmPassword' 
                            ? 'border-emerald-400 ring-4 ring-emerald-100 bg-white' 
                            : 'border-gray-200 bg-gray-50/50 hover:border-gray-300'
                        }`}
                        placeholder="ยืนยันรหัสผ่าน"
                        required
                      />
                    </div>
                    {formData.password && formData.confirmPassword && formData.password !== formData.confirmPassword && (
                      <p className="mt-3 text-sm text-red-600 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-2" /> รหัสผ่านไม่ตรงกัน
                      </p>
                    )}
                  </div>
                </div>
                
                <div className="flex justify-end pt-8">
                  <button
                    type="button"
                    onClick={nextStep}
                    disabled={!formData.fullName || !formData.email || !formData.gender || !formData.password || formData.password !== formData.confirmPassword}
                    className="flex items-center px-10 py-4 bg-gradient-to-r from-emerald-500 to-teal-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    ถัดไป <ArrowRight className="ml-3 w-5 h-5" />
                  </button>
                </div>
              </div>
            )}
            
            {/* Step 2: Body Information */}
            {currentStep === 2 && (
              <div className="space-y-8">
                <div className="text-center mb-10">
                  <h2 className="text-3xl font-bold text-gray-800 mb-3">ข้อมูลร่างกาย</h2>
                  <p className="text-gray-600">กรอกข้อมูลทางกายภาพเพื่อคำนวณความต้องการพลังงาน</p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      วันเกิด <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <Calendar className={`absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 transition-colors ${
                        focusedField === 'birthDate' ? 'text-emerald-500' : 'text-gray-400'
                      }`} />
                      <input
                        type="date"
                        name="birthDate"
                        value={formData.birthDate}
                        onChange={handleChange}
                        onFocus={() => setFocusedField('birthDate')}
                        onBlur={() => setFocusedField('')}
                        className={`pl-12 w-full py-4 border-2 rounded-2xl transition-all duration-300 text-black bg-white ${
                          focusedField === 'birthDate' 
                            ? 'border-emerald-400 ring-4 ring-emerald-100' 
                            : 'border-gray-200 bg-gray-50/50 hover:border-gray-300'
                        }`}
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      ส่วนสูง (ซม.) <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <Ruler className={`absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 transition-colors ${
                        focusedField === 'height' ? 'text-emerald-500' : 'text-gray-400'
                      }`} />
                      <input
                        type="number"
                        name="height"
                        value={formData.height}
                        onChange={handleChange}
                        onFocus={() => setFocusedField('height')}
                        onBlur={() => setFocusedField('')}
                        min="100"
                        max="250"
                        className={`pl-12 w-full py-4 border-2 rounded-2xl transition-all duration-300 text-black placeholder-gray-400 ${
                          focusedField === 'height' 
                            ? 'border-emerald-400 ring-4 ring-emerald-100 bg-white' 
                            : 'border-gray-200 bg-gray-50/50 hover:border-gray-300'
                        }`}
                        placeholder="เช่น 165"
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      น้ำหนัก (กก.) <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <Scale className={`absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 transition-colors ${
                        focusedField === 'weight' ? 'text-emerald-500' : 'text-gray-400'
                      }`} />
                      <input
                        type="number"
                        name="weight"
                        value={formData.weight}
                        onChange={handleChange}
                        onFocus={() => setFocusedField('weight')}
                        onBlur={() => setFocusedField('')}
                        min="30"
                        max="300"
                        step="0.1"
                        className={`pl-12 w-full py-4 border-2 rounded-2xl transition-all duration-300 text-black placeholder-gray-400 ${
                          focusedField === 'weight' 
                            ? 'border-emerald-400 ring-4 ring-emerald-100 bg-white' 
                            : 'border-gray-200 bg-gray-50/50 hover:border-gray-300'
                        }`}
                        placeholder="เช่น 65.5"
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="md:col-span-1">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      ระดับกิจกรรมทางกาย <span className="text-red-500">*</span>
                    </label>
                    <div className="space-y-3">
                      {activityOptions.map((option) => (
                        <label
                          key={option.value}
                          className={`relative flex items-center p-4 border-2 rounded-2xl cursor-pointer transition-all duration-300 ${
                            formData.activityLevel === option.value
                              ? 'border-emerald-400 bg-emerald-50 shadow-md'
                              : 'border-gray-200 bg-gray-50/50 hover:border-gray-300 hover:bg-white'
                          }`}
                        >
                          <input
                            type="radio"
                            name="activityLevel"
                            value={option.value}
                            checked={formData.activityLevel === option.value}
                            onChange={handleChange}
                            className="sr-only"
                          />
                          <span className="text-2xl mr-4">{option.icon}</span>
                          <div>
                            <div className="font-semibold text-gray-800">{option.label}</div>
                            <div className="text-sm text-gray-600">{option.detail}</div>
                          </div>
                          {formData.activityLevel === option.value && (
                            <Check className="absolute top-3 right-3 w-5 h-5 text-emerald-500" />
                          )}
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-between pt-8">
                  <button
                    type="button"
                    onClick={prevStep}
                    className="flex items-center px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-2xl hover:bg-gray-50 transition-all duration-300"
                  >
                    <ArrowLeft className="mr-3 w-5 h-5" /> ย้อนกลับ
                  </button>
                  
                  <button
                    type="button"
                    onClick={nextStep}
                    disabled={!formData.birthDate || !formData.height || !formData.weight}
                    className="flex items-center px-10 py-4 bg-gradient-to-r from-emerald-500 to-teal-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    ถัดไป <ArrowRight className="ml-3 w-5 h-5" />
                  </button>
                </div>
              </div>
            )}
            
            {/* Step 3: Goals */}
            {currentStep === 3 && (
              <div className="space-y-8">
                <div className="text-center mb-10">
                  <h2 className="text-3xl font-bold text-gray-800 mb-3">เป้าหมายของคุณ</h2>
                  <p className="text-gray-600">เลือกเป้าหมายเพื่อรับคำแนะนำที่เหมาะสมกับคุณ</p>
                </div>
                
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-6">
                    เป้าหมายหลักของคุณคืออะไร? <span className="text-red-500">*</span>
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {goalOptions.map((option) => (
                      <label
                        key={option.value}
                        className={`relative flex items-center p-6 border-2 rounded-2xl cursor-pointer transition-all duration-300 group ${
                          formData.goal === option.value
                            ? 'border-emerald-400 bg-emerald-50 shadow-lg scale-105'
                            : `border-gray-200 ${option.bg} hover:border-gray-300 hover:shadow-md hover:scale-102`
                        }`}
                      >
                        <input
                          type="radio"
                          name="goal"
                          value={option.value}
                          checked={formData.goal === option.value}
                          onChange={handleChange}
                          className="sr-only"
                        />
                        <div className={`w-12 h-12 rounded-xl flex items-center justify-center mr-4 bg-gradient-to-r ${option.color} text-white shadow-md`}>
                          <span className="text-xl">{option.icon}</span>
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold text-gray-800 text-lg">{option.label}</div>
                        </div>
                        {formData.goal === option.value && (
                          <Check className="absolute top-4 right-4 w-6 h-6 text-emerald-500" />
                        )}
                      </label>
                    ))}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    อาหารที่แพ้หรือข้อจำกัดทางอาหาร (ถ้ามี)
                  </label>
                  <textarea
                    name="foodAllergies"
                    value={formData.foodAllergies}
                    onChange={handleChange}
                    onFocus={() => setFocusedField('foodAllergies')}
                    onBlur={() => setFocusedField('')}
                    rows={4}
                    className={`w-full p-4 border-2 rounded-2xl transition-all duration-300 resize-none text-black placeholder-gray-400 ${
                      focusedField === 'foodAllergies' 
                        ? 'border-emerald-400 ring-4 ring-emerald-100 bg-white' 
                        : 'border-gray-200 bg-gray-50/50 hover:border-gray-300'
                    }`}
                    placeholder="ระบุอาหารที่คุณแพ้หรือไม่สามารถทานได้ เช่น กุ้ง ปลาทะเล ถั่วลิสง นม ไข่ ฯลฯ"
                  />
                </div>
                
                <div className="flex justify-between pt-8">
                  <button
                    type="button"
                    onClick={prevStep}
                    className="flex items-center px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-2xl hover:bg-gray-50 transition-all duration-300"
                  >
                    <ArrowLeft className="mr-3 w-5 h-5" /> ย้อนกลับ
                  </button>
                  
                  <button
                    type="button"
                    onClick={handleSubmit}
                    disabled={!formData.goal || loading}
                    className="flex items-center px-10 py-4 bg-gradient-to-r from-emerald-500 to-teal-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    {loading ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-3"></div>
                        กำลังลงทะเบียน...
                      </>
                    ) : (
                      <>
                        <Heart className="mr-3 w-5 h-5" />
                        ลงทะเบียน
                      </>
                    )}
                  </button>
                </div>
              </div>
            )}
            
            {/* Success/Error Message */}
            {message && (
              <div className={`mt-8 p-6 rounded-2xl flex items-center gap-4 transition-all duration-500 ${
                message.includes("สำเร็จ") 
                  ? 'bg-emerald-50 border-2 border-emerald-200 text-emerald-700' 
                  : 'bg-red-50 border-2 border-red-200 text-red-700'
              }`}>
                {message.includes("สำเร็จ") ? (
                  <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center">
                    <Check className="w-5 h-5 text-white" />
                  </div>
                ) : (
                  <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                    <AlertCircle className="w-5 h-5 text-white" />
                  </div>
                )}
                <span className="font-medium text-lg">{message}</span>
              </div>
            )}
          </div>
          
          {/* Footer */}
          <div className="px-10 py-8 bg-gradient-to-r from-gray-50/80 to-blue-50/80 backdrop-blur border-t border-gray-100">
            <p className="text-center text-gray-600 text-lg">
              มีบัญชีอยู่แล้ว?{" "}
              <a
                href="/login"
                className="font-semibold text-emerald-600 hover:text-emerald-700 underline decoration-emerald-300 hover:decoration-emerald-500 transition-colors"
              >
                เข้าสู่ระบบ
              </a>
            </p>
          </div>
        </div>
        
        {/* Feature Cards */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white/60 backdrop-blur-lg rounded-3xl p-8 border border-white/40 text-center hover:bg-white/80 transition-all duration-300 hover:scale-105">
            <div className="w-16 h-16 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
              <BookOpen className="w-8 h-8 text-white" />
            </div>
            <h3 className="font-bold text-xl text-gray-800 mb-4">วิเคราะห์โภชนาการ</h3>
            <p className="text-gray-600 leading-relaxed">ระบบคำนวณค่าพลังงานและสารอาหารแบบละเอียดตามหลักวิชาการโภชนาศาสตร์</p>
          </div>
          
          <div className="bg-white/60 backdrop-blur-lg rounded-3xl p-8 border border-white/40 text-center hover:bg-white/80 transition-all duration-300 hover:scale-105">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
              <Target className="w-8 h-8 text-white" />
            </div>
            <h3 className="font-bold text-xl text-gray-800 mb-4">เป้าหมายเฉพาะบุคคล</h3>
            <p className="text-gray-600 leading-relaxed">รับคำแนะนำที่เหมาะสมกับเป้าหมายและความต้องการของคุณโดยเฉพาะ</p>
          </div>
          
          <div className="bg-white/60 backdrop-blur-lg rounded-3xl p-8 border border-white/40 text-center hover:bg-white/80 transition-all duration-300 hover:scale-105">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <h3 className="font-bold text-xl text-gray-800 mb-4">ข้อมูลปลอดภัย</h3>
            <p className="text-gray-600 leading-relaxed">ข้อมูลส่วนตัวของคุณได้รับการปกป้องด้วยระบบความปลอดภัยขั้นสูง</p>
          </div>
        </div>
      </div>
    </div>
  );
}