
// src/app/api/login/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';
import { createRateLimiter, createRateLimitResponse, addRateLimitHeaders, resetRateLimit, getClientIdentifier } from '@/lib/rate-limit';
import { createSession, destroySession, type SessionUser } from '@/lib/session';

const prisma = new PrismaClient();

interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

function calculateAge(birthDate: Date): number {
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
}

export async function POST(req: NextRequest) {
  try {
    console.log('🔐 Login API called');

    // Rate limiting check
    const loginLimiter = createRateLimiter('login');
    const rateLimitResult = loginLimiter(req);

    if (!rateLimitResult.success) {
      console.log('🚫 Rate limit exceeded for login');
      return createRateLimitResponse(rateLimitResult);
    }

    const body: LoginRequest = await req.json();
    console.log('📝 Login attempt for:', body.email);

    const { email, password, rememberMe = false } = body;

    // Input validation
    if (!email || !password) {
      return NextResponse.json({
        error: 'กรอกชื่อผู้ใช้ และ รหัสผ่าน'
      }, { status: 400 });
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({
        error: 'ไม่ต้อง'
      }, { status: 400 });
    }

    // Find user by email (เลือกเฉพาะ fields <|im_start|>่ต้องใช้)
    const user = await prisma.user.findUnique({
      where: {
        email: email.toLowerCase()
      },
      select: {
        id: true,
        email: true,
        password: true,
        fullName: true,
        gender: true,
        height: true,
        weight: true,
        activityLevel: true,
        goal: true,
        foodAllergies: true,
        bmr: true,
        tdee: true,
        role: true,
        isActive: true,
        birthDate: true
      }
    });

    if (!user) {
      console.log('❌ User not found:', email);
      return NextResponse.json({
        error: 'ชื่อผู้ใช้ไม่มีในระบบ'
      }, { status: 401 });
    }

    // Check if user is active
    if (!user.isActive) {
      console.log('❌ User account is inactive:', email);
      return NextResponse.json({
        error: 'ชื่อผู้ใช้ถูกระงับ ต่อ้ใช้ระบบ'
      }, { status: 403 });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      console.log('❌ Invalid password for user:', email);
      return NextResponse.json({
        error: 'รหัสผ่านไม่ถูกต้อง'
      }, { status: 401 });
    }

    // Calculate age and BMI
    const age = calculateAge(user.birthDate);
    const bmi = Math.round((user.weight / Math.pow(user.height / 100, 2)) * 10) / 10;

    // Create session user object
    const sessionUser: SessionUser = {
      id: user.id,
      email: user.email,
      fullName: user.fullName,
      gender: user.gender,
      height: user.height,
      weight: user.weight,
      activityLevel: user.activityLevel,
      goal: user.goal,
      foodAllergies: user.foodAllergies || undefined,
      bmr: user.bmr || undefined,
      tdee: user.tdee || undefined,
      role: user.role,
      bmi,
      age,
      loginTime: Date.now(),
      lastActivity: Date.now()
    };

    console.log('✅ Login successful for user:', email);

    const response = NextResponse.json({
      message: 'เข้าสู่ระบบสำเร็จ! 🎉',
      user: sessionUser,
      sessionBased: true,
      autofillEnabled: rememberMe
    }, { status: 200 });

    // Create session with autofill credentials
    await createSession(
      req,
      response,
      sessionUser,
      { email, password, rememberMe },
      req.headers.get('user-agent') || undefined
    );

    // Reset rate limit for successful login (ไม่ count เมื่อ login สำเร็จ)
    const clientIdentifier = getClientIdentifier(req);
    resetRateLimit(clientIdentifier);

    // Add rate limit headers to successful response
    addRateLimitHeaders(response, rateLimitResult);

    return response;

  } catch (error: any) {
    console.error('❌ Login error:', error.message);

    // Handle specific Prisma errors
    if (error.code === 'P2025') {
      return NextResponse.json({
        error: 'รหัสผ่านไม่ต้อง'
      }, { status: 401 });
    }

    return NextResponse.json({
      error: 'ข้อผิดพลาดในระบบ ลองใหม่ครั้ง',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });

  } finally {
    await prisma.$disconnect();
  }
}

// Logout endpoint
export async function DELETE(req: NextRequest) {
  try {
    console.log('🚪 Logout API called');

    const response = NextResponse.json({
      message: 'ออกจากระบบสำเร็จ'
    }, { status: 200 });

    // Destroy session (but keep autofill credentials)
    await destroySession(req, response);

    console.log('✅ Session destroyed for logout');
    return response;

  } catch (error: any) {
    console.error('❌ Logout error:', error.message);
    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในการออกจากระบบ'
    }, { status: 500 });
  }
}
