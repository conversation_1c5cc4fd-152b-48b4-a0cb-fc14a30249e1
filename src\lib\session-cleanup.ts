// src/lib/session-cleanup.ts
import { cleanupExpiredSessions, SESSION_CONFIG } from './session';

let cleanupInterval: NodeJS.Timeout | null = null;

/**
 * Start automatic session cleanup
 */
export function startSessionCleanup(): void {
  if (cleanupInterval) {
    console.log('⚠️ Session cleanup already running');
    return;
  }

  const intervalMs = SESSION_CONFIG.CLEANUP_INTERVAL_HOURS * 60 * 60 * 1000;
  
  cleanupInterval = setInterval(async () => {
    try {
      console.log('🧹 Running automatic session cleanup...');
      const cleanedCount = await cleanupExpiredSessions();
      console.log(`✅ Automatic cleanup completed: ${cleanedCount} sessions cleaned`);
    } catch (error) {
      console.error('❌ Automatic session cleanup failed:', error);
    }
  }, intervalMs);

  console.log(`✅ Session cleanup started (interval: ${SESSION_CONFIG.CLEANUP_INTERVAL_HOURS} hours)`);
}

/**
 * Stop automatic session cleanup
 */
export function stopSessionCleanup(): void {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    cleanupInterval = null;
    console.log('✅ Session cleanup stopped');
  }
}

/**
 * Run cleanup once immediately
 */
export async function runCleanupNow(): Promise<number> {
  try {
    console.log('🧹 Running manual session cleanup...');
    const cleanedCount = await cleanupExpiredSessions();
    console.log(`✅ Manual cleanup completed: ${cleanedCount} sessions cleaned`);
    return cleanedCount;
  } catch (error) {
    console.error('❌ Manual session cleanup failed:', error);
    return 0;
  }
}

// Auto-start cleanup in production
if (process.env.NODE_ENV === 'production') {
  startSessionCleanup();
}
