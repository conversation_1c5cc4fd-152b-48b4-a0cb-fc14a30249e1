// src/components/SessionExpiredModal.tsx
"use client";

import { useEffect, useState } from 'react';
import { AlertTriangle, LogOut } from 'lucide-react';

interface SessionExpiredModalProps {
  isOpen: boolean;
  onConfirm: () => void;
  reason?: string;
}

export default function SessionExpiredModal({ 
  isOpen, 
  onConfirm, 
  reason = 'มีการเข้าสู่ระบบจากอุปกรณ์อื่น' 
}: SessionExpiredModalProps) {
  const [countdown, setCountdown] = useState(10);

  useEffect(() => {
    if (!isOpen) return;

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          onConfirm();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isOpen, onConfirm]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl border border-gray-200 animate-in fade-in-0 zoom-in-95 duration-300">
        <div className="text-center">
          {/* Icon */}
          <div className="w-16 h-16 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
            <AlertTriangle className="w-8 h-8 text-white" />
          </div>

          {/* Title */}
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            คุณได้ถูกออกจากระบบ
          </h2>

          {/* Message */}
          <div className="text-gray-600 mb-6 space-y-2">
            <p className="text-lg">
              {reason}
            </p>
            <p className="text-sm">
              เพื่อความปลอดภัยของบัญชี กรุณาเข้าสู่ระบบใหม่
            </p>
          </div>

          {/* Countdown */}
          <div className="bg-gray-50 rounded-2xl p-4 mb-6">
            <p className="text-sm text-gray-600 mb-2">
              จะนำทางไปหน้าเข้าสู่ระบบอัตโนมัติใน
            </p>
            <div className="text-3xl font-bold text-red-600">
              {countdown}
            </div>
            <p className="text-xs text-gray-500 mt-1">วินาที</p>
          </div>

          {/* Action Button */}
          <button
            onClick={onConfirm}
            className="w-full flex items-center justify-center gap-3 bg-gradient-to-r from-red-500 to-red-600 text-white py-4 px-6 rounded-2xl font-semibold hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
          >
            <LogOut className="w-5 h-5" />
            ไปหน้าเข้าสู่ระบบทันที
          </button>

          {/* Additional Info */}
          <div className="mt-6 p-4 bg-amber-50 rounded-xl border border-amber-200">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="text-left">
                <p className="text-sm font-medium text-amber-800 mb-1">
                  เกิดอะไรขึ้น?
                </p>
                <p className="text-xs text-amber-700">
                  ระบบตรวจพบการเข้าสู่ระบบจากอุปกรณ์อื่น เพื่อความปลอดภัย 
                  เราจึงออกจากระบบในอุปกรณ์นี้โดยอัตโนมัติ
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
