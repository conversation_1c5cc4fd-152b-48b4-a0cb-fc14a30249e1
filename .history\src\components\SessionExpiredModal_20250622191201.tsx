// src/components/SessionExpiredModal.tsx
"use client";

import { useEffect, useState } from 'react';
import { AlertTriangle, LogOut } from 'lucide-react';

interface SessionExpiredModalProps {
  isOpen: boolean;
  onConfirm: () => void;
  onCancel?: () => void;
  reason?: string;
}

export default function SessionExpiredModal({
  isOpen,
  onConfirm,
  onCancel,
  reason = 'มีการเข้าสู่ระบบจากอุปกรณ์อื่น'
}: SessionExpiredModalProps) {
  const [countdown, setCountdown] = useState(30); // เพิ่มเป็น 30 วินาที
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      setShowModal(false);
      return;
    }

    // แสดง modal ทันที
    setShowModal(true);
    setCountdown(30); // Reset countdown เป็น 30 วินาที

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          onConfirm();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isOpen, onConfirm]);

  if (!isOpen || !showModal) return null;

  return (
    <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg p-6 max-w-sm w-full shadow-lg border border-gray-200 animate-in fade-in-0 zoom-in-95 duration-200">
        <div className="text-center">
          {/* Simple Icon */}
          <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertTriangle className="w-6 h-6 text-red-600" />
          </div>

          {/* Title */}
          <h2 className="text-lg font-semibold text-gray-800 mb-3">
            Session หมดอายุ
          </h2>

          {/* Simple Message */}
          <p className="text-gray-600 text-sm mb-4">
            {reason}
          </p>

          {/* Additional Info */}
          <div className="bg-amber-50 rounded-lg p-3 mb-4 border border-amber-200">
            <p className="text-xs text-amber-700">
              💡 เพื่อความปลอดภัย ระบบอนุญาตให้เข้าสู่ระบบได้เพียงอุปกรณ์เดียวเท่านั้น
            </p>
          </div>

          {/* Simple Countdown */}
          <div className="bg-gray-50 rounded-lg p-3 mb-4">
            <p className="text-xs text-gray-500 mb-1">
              จะนำทางไปหน้าเข้าสู่ระบบใน
            </p>
            <div className="text-xl font-bold text-red-600">
              {countdown} วินาที
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1 mt-2">
              <div
                className="bg-red-600 h-1 rounded-full transition-all duration-1000 ease-linear"
                style={{ width: `${(countdown / 30) * 100}%` }}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            {onCancel && (
              <button
                onClick={onCancel}
                className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
              >
                ยกเลิก
              </button>
            )}
            <button
              onClick={onConfirm}
              className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
            >
              เข้าสู่ระบบใหม่
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
