import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// POST /api/ffq/submissions
// สร้าง submission ใหม่พร้อมกับคำตอบทั้งหมด
export async function POST(request: Request) {
  const body = await request.json();
  const { userId, ffqSetId, answers } = body;

  if (!userId || !ffqSetId || !answers || !Array.isArray(answers)) {
    return new NextResponse('Missing or invalid required fields', { status: 400 });
  }

  try {
    const submission = await prisma.fFQSubmission.create({
      data: {
        userId: userId,
        ffqSetId: ffqSetId,
        answers: {
          create: answers.map((ans: { questionId: number; answer: any }) => ({
            questionId: ans.questionId,
            answer: ans.answer,
          })),
        },
      },
      include: {
        answers: true, // ส่งข้อมูลคำตอบกลับไปด้วย
      },
    });

    return NextResponse.json(submission, { status: 201 });
  } catch (error) {
    console.error('Error creating FFQ submission:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
