"use client";

import { useState, useEffect } from "react";
import {
  Mail, Lock, Eye, EyeOff, ArrowRight,
  Utensils, Shield, Heart, User, CheckCircle,
  AlertCircle, Sparkles, Coffee, TrendingUp,
  ChevronDown, Clock, Trash2
} from "lucide-react";

interface SavedCredential {
  email: string;
  password: string;
  displayName?: string;
  lastUsed: string;
}

export default function ModernLoginPage() {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    rememberMe: false
  });

  const [showPassword, setShowPassword] = useState(false);
  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [focusedField, setFocusedField] = useState("");
  const [savedCredentials, setSavedCredentials] = useState<SavedCredential[]>([]);
  const [showAutofillDropdown, setShowAutofillDropdown] = useState(false);

  // Check if user is already logged in and load saved credentials
  useEffect(() => {
    const initializePage = async () => {
      try {
        // Check session
        const sessionResponse = await fetch('/api/session', {
          method: 'GET',
          credentials: 'include'
        });

        if (sessionResponse.ok) {
          // User is already logged in, redirect to home
          window.location.href = "/";
          return;
        }

        // Load saved credentials for autofill
        const autofillResponse = await fetch('/api/autofill', {
          method: 'GET',
          credentials: 'include'
        });

        if (autofillResponse.ok) {
          const data = await autofillResponse.json();
          setSavedCredentials(data.credentials || []);
        }
      } catch (error) {
        console.log('Initialization error:', error);
      }
    };

    initializePage();
  }, []);

  const handleChange = (e: { target: { name: any; value: any; type: any; checked: any; }; }) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle autofill selection
  const handleAutofillSelect = (credential: SavedCredential) => {
    setFormData({
      email: credential.email,
      password: credential.password,
      rememberMe: true
    });
    setShowAutofillDropdown(false);
  };

  // Handle autofill dropdown toggle
  const toggleAutofillDropdown = () => {
    setShowAutofillDropdown(!showAutofillDropdown);
  };

  const handleSubmit = async (e: { preventDefault: () => void; }) => {
    e.preventDefault();
    setMessage("");
    setLoading(true);
    
    try {
      const res = await fetch("/api/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: 'include', // Include cookies for session
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
          rememberMe: formData.rememberMe // For autofill
        }),
      });

      const data = await res.json();

      if (res.ok) {
        setMessage(data.message || "เข้าสู่ระบบสำเร็จ! กำลังนำทางไปหน้าหลัก...");

        // Session-based authentication with autofill
        console.log('✅ Session-based login successful');
        if (data.autofillEnabled) {
          console.log('✅ Credentials saved for autofill');
        }

        // Redirect to home page
        setTimeout(() => {
          window.location.href = "/";
        }, 1500);
      } else {
        setMessage(data.error || "อีเมลหรือรหัสผ่านไม่ถูกต้อง");
      }
      
    } catch (error) {
      setMessage("เกิดข้อผิดพลาดในการเชื่อมต่อ กรุณาลองใหม่อีกครั้ง");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center py-8 px-4 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 rounded-full bg-gradient-to-br from-blue-200/30 to-indigo-300/30 blur-3xl" />
        <div className="absolute top-1/2 -left-40 w-96 h-96 rounded-full bg-gradient-to-br from-emerald-200/30 to-teal-300/30 blur-3xl" />
        <div className="absolute -bottom-40 right-1/3 w-80 h-80 rounded-full bg-gradient-to-br from-purple-200/30 to-pink-300/30 blur-3xl" />
      </div>
      
      <div className="relative z-10 w-full max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        
        {/* Left Side - Welcome Content */}
        <div className="hidden lg:block space-y-8">
          <div className="text-center lg:text-left">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-3xl mb-8 shadow-2xl">
              <Utensils className="w-10 h-10 text-white" />
            </div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-emerald-600 via-teal-600 to-blue-600 bg-clip-text text-transparent mb-6">
              ยินดีต้อนรับกลับ!
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              เข้าสู่ระบบเพื่อติดตามการทานอาหารและ<br />
              วิเคราะห์โภชนาการของคุณต่อ
            </p>
          </div>
          
          {/* Feature Highlights */}
          <div className="space-y-6">
            <div className="flex items-center gap-4 p-4 bg-white/60 backdrop-blur-lg rounded-2xl border border-white/40">
              <div className="w-12 h-12 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-xl flex items-center justify-center shadow-md">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-800">ติดตามความคืบหน้า</h3>
                <p className="text-sm text-gray-600">ดูสถิติและการพัฒนาของคุณ</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 p-4 bg-white/60 backdrop-blur-lg rounded-2xl border border-white/40">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-xl flex items-center justify-center shadow-md">
                <Coffee className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-800">บันทึกง่ายๆ ทุกมื้อ</h3>
                <p className="text-sm text-gray-600">จดบันทึกอาหารและเครื่องดื่ม</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4 p-4 bg-white/60 backdrop-blur-lg rounded-2xl border border-white/40">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-xl flex items-center justify-center shadow-md">
                <Heart className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-800">คำแนะนำส่วนตัว</h3>
                <p className="text-sm text-gray-600">รับคำแนะนำเฉพาะบุคคล</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Right Side - Login Form */}
        <div className="w-full max-w-md mx-auto lg:mx-0">
          <div className="bg-white/70 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/50 overflow-hidden">
            <div className="p-8">
              
              {/* Mobile Header */}
              <div className="text-center mb-8 lg:hidden">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl mb-4 shadow-lg">
                  <Utensils className="w-8 h-8 text-white" />
                </div>
                <h2 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  เข้าสู่ระบบ
                </h2>
              </div>
              
              {/* Desktop Header */}
              <div className="hidden lg:block text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-800 mb-2">เข้าสู่ระบบ</h2>
                <p className="text-gray-600">กรอกข้อมูลเพื่อเข้าใช้งาน</p>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Email Field with Autofill */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <label className="block text-sm font-semibold text-gray-700">
                      อีเมล <span className="text-red-500">*</span>
                    </label>
                    {savedCredentials.length > 0 && (
                      <button
                        type="button"
                        onClick={toggleAutofillDropdown}
                        className="flex items-center gap-2 text-xs text-emerald-600 hover:text-emerald-700 font-medium"
                      >
                        <Clock className="w-3 h-3" />
                        บัญชีที่บันทึกไว้ ({savedCredentials.length})
                        <ChevronDown className={`w-3 h-3 transition-transform ${showAutofillDropdown ? 'rotate-180' : ''}`} />
                      </button>
                    )}
                  </div>

                  <div className="relative">
                    <Mail className={`absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 transition-colors ${
                      focusedField === 'email' ? 'text-emerald-500' : 'text-gray-400'
                    }`} />
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      onFocus={() => setFocusedField('email')}
                      onBlur={() => setFocusedField('')}
                      className={`pl-12 pr-4 w-full py-4 border-2 rounded-2xl transition-all duration-300 text-black placeholder-gray-400 ${
                        focusedField === 'email'
                          ? 'border-emerald-400 ring-4 ring-emerald-100 bg-white'
                          : 'border-gray-200 bg-gray-50/50 hover:border-gray-300'
                      }`}
                      placeholder="กรอกอีเมลของคุณ"
                      required
                    />
                    {formData.email && (
                      <CheckCircle className="absolute right-4 top-1/2 -translate-y-1/2 w-5 h-5 text-emerald-500" />
                    )}
                  </div>

                  {/* Autofill Dropdown */}
                  {showAutofillDropdown && savedCredentials.length > 0 && (
                    <div className="mt-2 bg-white border-2 border-gray-200 rounded-2xl shadow-lg overflow-hidden">
                      <div className="p-3 bg-gray-50 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-medium text-gray-700">บัญชีที่บันทึกไว้</span>
                          <button
                            type="button"
                            className="text-xs text-red-600 hover:text-red-700 flex items-center gap-1"
                          >
                            <Trash2 className="w-3 h-3" />
                            ลบทั้งหมด
                          </button>
                        </div>
                      </div>
                      <div className="max-h-48 overflow-y-auto">
                        {savedCredentials.map((credential, index) => (
                          <button
                            key={index}
                            type="button"
                            onClick={() => handleAutofillSelect(credential)}
                            className="w-full p-4 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0"
                          >
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-lg flex items-center justify-center">
                                <User className="w-4 h-4 text-white" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="font-medium text-gray-800 truncate">
                                  {credential.displayName || credential.email}
                                </div>
                                <div className="text-xs text-gray-500 truncate">
                                  {credential.email}
                                </div>
                                <div className="text-xs text-gray-400">
                                  ใช้ล่าสุด: {credential.lastUsed}
                                </div>
                              </div>
                              <div className="text-xs text-gray-400">
                                ••••••••
                              </div>
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Password Field */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-3">
                    รหัสผ่าน <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <Lock className={`absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 transition-colors ${
                      focusedField === 'password' ? 'text-emerald-500' : 'text-gray-400'
                    }`} />
                    <input
                      type={showPassword ? 'text' : 'password'}
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      onFocus={() => setFocusedField('password')}
                      onBlur={() => setFocusedField('')}
                      className={`pl-12 pr-12 w-full py-4 border-2 rounded-2xl transition-all duration-300 text-black placeholder-gray-400 ${
                        focusedField === 'password' 
                          ? 'border-emerald-400 ring-4 ring-emerald-100 bg-white' 
                          : 'border-gray-200 bg-gray-50/50 hover:border-gray-300'
                      }`}
                      placeholder="กรอกรหัสผ่าน"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>
                </div>
                
                {/* Remember Me & Forgot Password */}
                <div className="flex items-center justify-between">
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      name="rememberMe"
                      checked={formData.rememberMe}
                      onChange={handleChange}
                      className="w-4 h-4 text-emerald-600 border-gray-300 rounded focus:ring-emerald-500 transition-colors"
                    />
                    <span className="ml-2 text-sm text-gray-600">จดจำการเข้าสู่ระบบ</span>
                  </label>
                  <button
                    type="button"
                    className="text-sm text-emerald-600 hover:text-emerald-700 font-medium underline decoration-emerald-300 hover:decoration-emerald-500 transition-colors"
                  >
                    ลืมรหัสผ่าน?
                  </button>
                </div>
                
                {/* Login Button */}
                <button
                  type="submit"
                  disabled={!formData.email || !formData.password || loading}
                  className="w-full py-4 bg-gradient-to-r from-emerald-500 to-teal-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center"
                >
                  {loading ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-3"></div>
                      กำลังเข้าสู่ระบบ...
                    </>
                  ) : (
                    <>
                      เข้าสู่ระบบ
                      <ArrowRight className="ml-3 w-5 h-5" />
                    </>
                  )}
                </button>
                
                {/* Success/Error Message */}
                {message && (
                  <div className={`p-4 rounded-2xl flex items-center gap-3 transition-all duration-500 ${
                    message.includes("สำเร็จ") 
                      ? 'bg-emerald-50 border-2 border-emerald-200 text-emerald-700' 
                      : 'bg-red-50 border-2 border-red-200 text-red-700'
                  }`}>
                    {message.includes("สำเร็จ") ? (
                      <div className="w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-white" />
                      </div>
                    ) : (
                      <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                        <AlertCircle className="w-4 h-4 text-white" />
                      </div>
                    )}
                    <span className="font-medium">{message}</span>
                  </div>
                )}
              </form>
            </div>
            
            {/* Footer */}
            <div className="px-8 py-6 bg-gradient-to-r from-gray-50/80 to-blue-50/80 backdrop-blur border-t border-gray-100">
              <p className="text-center text-gray-600">
                ยังไม่มีบัญชี?{" "}
                <a
                  href="/register"
                  className="font-semibold text-emerald-600 hover:text-emerald-700 underline decoration-emerald-300 hover:decoration-emerald-500 transition-colors"
                >
                  สมัครสมาชิก
                </a>
              </p>
            </div>
          </div>
          
          {/* Mobile Feature Cards */}
          <div className="mt-8 lg:hidden">
            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-center gap-3 p-4 bg-white/60 backdrop-blur-lg rounded-2xl border border-white/40">
                <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-lg flex items-center justify-center">
                  <Shield className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 text-sm">ความปลอดภัยสูง</h3>
                  <p className="text-xs text-gray-600">ข้อมูลได้รับการปกป้อง</p>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-4 bg-white/60 backdrop-blur-lg rounded-2xl border border-white/40">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-lg flex items-center justify-center">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 text-sm">ประสบการณ์ที่ดี</h3>
                  <p className="text-xs text-gray-600">ใช้งานง่าย เข้าใจง่าย</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}