// C:\SofterM Github\Project-nutrition\src\components\NutritionTips.tsx
"use client";

import { BookOpen, Apple, Heart } from "lucide-react";

export default function NutritionTips() {
  return (
    <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-6 border border-white/50 shadow-lg">
      <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
        <BookOpen className="w-5 h-5 text-emerald-600" />
        เทคนิคโภชนาการ
      </h3>
      
      <div className="space-y-4">
        <div className="p-4 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl border border-emerald-100">
          <div className="flex items-start gap-3">
            <Apple className="w-5 h-5 text-emerald-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-emerald-800 mb-1">ผักผลไม้ 5 สี</h4>
              <p className="text-sm text-emerald-700">ทานผักผลไม้หลากสีเพื่อรับวิตามินครบถ้วน</p>
            </div>
          </div>
        </div>
        
        <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
          <div className="flex items-start gap-3">
            <Heart className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-800 mb-1">ดื่มน้ำเพียงพอ</h4>
              <p className="text-sm text-blue-700">ดื่มน้ำอย่างน้อย 8 แก้วต่อวัน</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}