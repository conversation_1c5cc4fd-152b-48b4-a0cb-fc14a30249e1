import type { <PERSON>ada<PERSON> } from "next";
import { Kanit } from "next/font/google";
import "./globals.css";

const kanit = Kanit({
  variable: "--font-kanit",
  subsets: ["latin", "thai"],
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "ระบบบันทึกโภชนาการ",
  description: "ระบบจัดการและบันทึกข้อมูลโภชนาการ",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="th">
      <body
        className={`${kanit.variable} antialiased font-kanit`}
      >
        {children}
      </body>
    </html>
  );
}