// src/app/api/sessions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { 
  getSession, 
  getActiveSessions, 
  forceLogoutOtherSessions,
  invalidateSessionInDB,
  cleanupExpiredSessions,
  parseDeviceInfo
} from '@/lib/session';

// GET /api/sessions - ดู active sessions ทั้งหมดของผู้ใช้
export async function GET(req: NextRequest) {
  try {
    console.log('📱 Get active sessions API called');

    const session = await getSession(req);

    if (!session.isLoggedIn || !session.user) {
      return NextResponse.json({
        error: 'ไม่ได้เข้าสู่ระบบ'
      }, { status: 401 });
    }

    // Get all active sessions for this user
    const activeSessions = await getActiveSessions(session.user.id);
    
    // Mark current session
    const sessionsWithCurrent = activeSessions.map(s => ({
      ...s,
      isCurrent: s.id === session.sessionId,
      loginTime: s.loginTime.toISOString(),
      lastActivity: s.lastActivity.toISOString(),
      expiresAt: s.expiresAt.toISOString(),
    }));

    return NextResponse.json({
      message: 'ดึงข้อมูล active sessions สำเร็จ',
      sessions: sessionsWithCurrent,
      totalSessions: activeSessions.length,
      currentSessionId: session.sessionId
    }, { status: 200 });

  } catch (error: any) {
    console.error('❌ Get sessions error:', error.message);
    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในการดึงข้อมูล sessions',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}

// DELETE /api/sessions - ลบ session ที่ระบุ หรือ logout all other sessions
export async function DELETE(req: NextRequest) {
  try {
    console.log('🚪 Delete session API called');

    const session = await getSession(req);

    if (!session.isLoggedIn || !session.user) {
      return NextResponse.json({
        error: 'ไม่ได้เข้าสู่ระบบ'
      }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const sessionId = searchParams.get('sessionId');
    const action = searchParams.get('action'); // 'logout_others' or 'logout_session'

    if (action === 'logout_others') {
      // Logout all other sessions except current
      if (!session.sessionId) {
        return NextResponse.json({
          error: 'ไม่พบ session ID ปัจจุบัน'
        }, { status: 400 });
      }

      const loggedOutCount = await forceLogoutOtherSessions(session.user.id, session.sessionId);

      return NextResponse.json({
        message: `ออกจากระบบ ${loggedOutCount} sessions อื่นสำเร็จ`,
        loggedOutCount
      }, { status: 200 });

    } else if (action === 'logout_session' && sessionId) {
      // Logout specific session
      if (sessionId === session.sessionId) {
        return NextResponse.json({
          error: 'ไม่สามารถออกจาก session ปัจจุบันได้'
        }, { status: 400 });
      }

      // Verify that this session belongs to the current user
      const activeSessions = await getActiveSessions(session.user.id);
      const targetSession = activeSessions.find(s => s.id === sessionId);

      if (!targetSession) {
        return NextResponse.json({
          error: 'ไม่พบ session ที่ระบุ'
        }, { status: 404 });
      }

      await invalidateSessionInDB(sessionId, 'forced_logout');

      return NextResponse.json({
        message: 'ออกจาก session สำเร็จ',
        sessionId
      }, { status: 200 });

    } else if (action === 'cleanup') {
      // Cleanup expired sessions (admin function)
      const cleanedCount = await cleanupExpiredSessions();

      return NextResponse.json({
        message: `ทำความสะอาด ${cleanedCount} expired sessions สำเร็จ`,
        cleanedCount
      }, { status: 200 });

    } else {
      return NextResponse.json({
        error: 'ระบุ action ไม่ถูกต้อง (logout_others, logout_session, cleanup)'
      }, { status: 400 });
    }

  } catch (error: any) {
    console.error('❌ Delete session error:', error.message);
    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในการลบ session',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}

// POST /api/sessions - สร้าง session ใหม่ (สำหรับ testing)
export async function POST(req: NextRequest) {
  try {
    console.log('🔄 Create session API called');

    const session = await getSession(req);

    if (!session.isLoggedIn || !session.user) {
      return NextResponse.json({
        error: 'ไม่ได้เข้าสู่ระบบ'
      }, { status: 401 });
    }

    // Parse current device info
    const deviceInfo = parseDeviceInfo(req);

    return NextResponse.json({
      message: 'ข้อมูล device ปัจจุบัน',
      deviceInfo,
      currentSession: {
        id: session.sessionId,
        userId: session.user.id,
        loginTime: new Date(session.user.loginTime).toISOString(),
        lastActivity: new Date(session.user.lastActivity).toISOString()
      }
    }, { status: 200 });

  } catch (error: any) {
    console.error('❌ Create session error:', error.message);
    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในการสร้าง session',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}
