//src\app\api\food-logs\route.ts
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getSession } from '../../../lib/session';

const prisma = new PrismaClient();

// --- In-memory rate limit for GET requests ---
type RateLimitEntry = { count: number; start: number };
type RateLimitStore = Record<string, RateLimitEntry>;

const RATE_LIMIT_MAX = parseInt(process.env.RATE_LIMIT_MAX || '100');
const RATE_LIMIT_WINDOW = parseInt(process.env.RATE_LIMIT_WINDOW || '15'); // in minutes

// Use globalThis to persist across hot reloads
interface GlobalWithRateLimit {
  _foodLogsRateLimitStore?: RateLimitStore;
}
const globalWithRL = globalThis as GlobalWithRateLimit;
const rlStore: RateLimitStore = globalWithRL._foodLogsRateLimitStore ?? {};
globalWithRL._foodLogsRateLimitStore = rlStore;

function getRateLimitKey(req: NextRequest, userId: string | undefined) {
  // Use userId if available, otherwise fallback to IP
  const ip = req.headers.get('x-forwarded-for') || 'unknown';
  return userId ? `user:${userId}` : `ip:${ip}`;
}

function isRateLimited(key: string) {
  const now = Date.now();
  const windowMs = RATE_LIMIT_WINDOW * 60 * 1000;
  if (!rlStore[key]) {
    rlStore[key] = { count: 1, start: now };
    return { limited: false, remaining: RATE_LIMIT_MAX - 1 };
  }
  const { count, start } = rlStore[key];
  if (now - start > windowMs) {
    rlStore[key] = { count: 1, start: now };
    return { limited: false, remaining: RATE_LIMIT_MAX - 1 };
  }
  if (count >= RATE_LIMIT_MAX) {
    return { limited: true, remaining: 0 };
  }
  rlStore[key].count++;
  return { limited: false, remaining: RATE_LIMIT_MAX - rlStore[key].count };
}

// POST: Save a meal log for the logged-in user
export async function POST(req: NextRequest) {
  const session = await getSession(req);
  if (!session.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const userId = session.user.id;
  const data = await req.json();

  // Expect: mealType, date, time, selectedFoods, nutrition, beforeImage, afterImage, menuDetail
  try {
    const mealDate = new Date(data.date + 'T' + (data.time || '00:00'));
    const foodLog = await prisma.foodLog.create({
      data: {
        userId,
        date: new Date(data.date),
        mealType: data.mealType,
        mealTime: mealDate,
        beforeImage: data.beforeImage || null, // fix: use correct field from frontend
        afterImage: data.afterImage || null,   // fix: use correct field from frontend
        items: {
          create: data.selectedFoods.map((item: SelectedFood) => ({
            foodId: parseInt(item.food.id),
            amount: item.quantity,
            unit: item.unit,
            calories: item.food.calories_per_unit * item.quantity,
            protein: item.food.protein_per_unit * item.quantity,
            carbs: item.food.carbs_per_unit * item.quantity,
            fat: item.food.fat_per_unit * item.quantity,
          })),
        },
        notes: data.menuDetail || null,
      },
      include: { items: true },
    });
    return NextResponse.json({ success: true, foodLog });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Failed to save food log' }, { status: 500 });
  }
}

// GET: Get all food logs for the logged-in user (optionally filter by date)
export async function GET(req: NextRequest) {
  const session = await getSession(req);
  if (!session.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const userId = Number(session.user.id);
  // --- Rate limit check ---
  const rlKey = getRateLimitKey(req, String(userId));
  const rlResult = isRateLimited(rlKey);
  if (rlResult.limited) {
    return NextResponse.json({ error: 'Rate limit exceeded. Please try again later.' }, { status: 429 });
  }
  const { searchParams } = new URL(req.url);
  const date = searchParams.get('date');
  const where: { userId: number; date?: Date } = { userId };
  if (date) {
    // Filter logs for the specific date
    where.date = new Date(date);
  }
  try {
    const foodLogs = await prisma.foodLog.findMany({
      where,
      include: {
        items: { include: { food: true } },
      },
      orderBy: { mealTime: 'asc' },
    });
    return NextResponse.json({ foodLogs });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ error: 'Failed to fetch food logs' }, { status: 500 });
  }
}

// Fix for selectedFoods.map((item: any) => ...)
type SelectedFood = { foodId: string; amount: number; [key: string]: any };
