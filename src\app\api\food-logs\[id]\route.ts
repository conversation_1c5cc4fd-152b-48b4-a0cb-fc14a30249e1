// src/app/api/food-logs/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { getSession } from '../../../../lib/session';

const prisma = new PrismaClient();

// PUT: Update existing meal log
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getSession(req);
  if (!session.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const userId = session.user.id;
  const foodLogId = parseInt(params.id);
  const data = await req.json();

  try {
    // ตรวจสอบว่า food log นี้เป็นของ user ที่ login อยู่หรือไม่
    const existingLog = await prisma.foodLog.findFirst({
      where: {
        id: foodLogId,
        userId: userId
      }
    });

    if (!existingLog) {
      return NextResponse.json(
        { error: 'Food log not found or unauthorized' }, 
        { status: 404 }
      );
    }

    // อัปเดต food log พร้อมกับลบ items เดิมและสร้างใหม่
    const mealDate = new Date(data.date + 'T' + (data.time || '00:00'));
    
    const updatedFoodLog = await prisma.foodLog.update({
      where: { id: foodLogId },
      data: {
        date: new Date(data.date),
        mealType: data.mealType,
        mealTime: mealDate,
        beforeImage: data.beforeImage || null,
        afterImage: data.afterImage || null,
        notes: data.menuDetail || null,
        // ลบ items เดิมทั้งหมดและสร้างใหม่
        items: {
          deleteMany: {}, // ลบ items เดิมทั้งหมด
          create: data.selectedFoods.map((item: any) => ({
            foodId: parseInt(item.food.id),
            amount: item.quantity,
            unit: item.unit,
            calories: item.food.calories_per_unit * item.quantity,
            protein: item.food.protein_per_unit * item.quantity,
            carbs: item.food.carbs_per_unit * item.quantity,
            fat: item.food.fat_per_unit * item.quantity,
          })),
        },
      },
      include: { 
        items: {
          include: {
            food: true
          }
        }
      },
    });

    return NextResponse.json({ 
      success: true, 
      foodLog: updatedFoodLog,
      message: 'อัปเดตข้อมูลสำเร็จ'
    });

  } catch (error) {
    console.error('Update food log error:', error);
    return NextResponse.json(
      { error: 'Failed to update food log' }, 
      { status: 500 }
    );
  }
}

// DELETE: Delete food log (optional - for future use)
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getSession(req);
  if (!session.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const userId = session.user.id;
  const foodLogId = parseInt(params.id);

  try {
    // ตรวจสอบเจ้าของก่อนลบ
    const existingLog = await prisma.foodLog.findFirst({
      where: {
        id: foodLogId,
        userId: userId
      }
    });

    if (!existingLog) {
      return NextResponse.json(
        { error: 'Food log not found or unauthorized' }, 
        { status: 404 }
      );
    }

    // ลบ food log (items จะถูกลบตาม cascade)
    await prisma.foodLog.delete({
      where: { id: foodLogId }
    });

    return NextResponse.json({ 
      success: true,
      message: 'ลบข้อมูลสำเร็จ'
    });

  } catch (error) {
    console.error('Delete food log error:', error);
    return NextResponse.json(
      { error: 'Failed to delete food log' }, 
      { status: 500 }
    );
  }
}

// GET: Get specific food log (optional - for future use)
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getSession(req);
  if (!session.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const userId = session.user.id;
  const foodLogId = parseInt(params.id);

  try {
    const foodLog = await prisma.foodLog.findFirst({
      where: {
        id: foodLogId,
        userId: userId
      },
      include: {
        items: {
          include: {
            food: true
          }
        }
      }
    });

    if (!foodLog) {
      return NextResponse.json(
        { error: 'Food log not found' }, 
        { status: 404 }
      );
    }

    return NextResponse.json({ foodLog });

  } catch (error) {
    console.error('Get food log error:', error);
    return NextResponse.json(
      { error: 'Failed to get food log' }, 
      { status: 500 }
    );
  }
}