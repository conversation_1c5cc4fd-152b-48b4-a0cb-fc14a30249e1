@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --font-kanit: 'Kanit', sans-serif;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-kanit);
}

/* Custom animations for smooth transitions */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out forwards;
}

/* Smooth text transitions */
.text-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
