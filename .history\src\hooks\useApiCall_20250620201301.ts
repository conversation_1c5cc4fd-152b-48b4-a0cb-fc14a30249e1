// src/hooks/useApiCall.ts
"use client";

import { useAuth } from '@/components/AuthProvider';
import { useCallback } from 'react';

interface ApiCallOptions extends RequestInit {
  showErrorAlert?: boolean;
}

export function useApiCall() {
  const { logout } = useAuth();

  const apiCall = useCallback(async (
    url: string, 
    options: ApiCallOptions = {}
  ): Promise<Response> => {
    const { showErrorAlert = true, ...fetchOptions } = options;

    try {
      const response = await fetch(url, {
        credentials: 'include', // Always include cookies for session
        ...fetchOptions
      });

      // Check for 401 Unauthorized (session invalidated)
      if (response.status === 401) {
        console.log('🚨 401 Unauthorized - Session invalidated, auto-logout');
        
        // Auto-logout user
        logout();
        
        // Show notification
        if (showErrorAlert && typeof window !== 'undefined') {
          alert('คุณได้ถูกออกจากระบบเนื่องจากมีการเข้าสู่ระบบจากอุปกรณ์อื่น');
          window.location.href = '/login';
        }
        
        throw new Error('Session invalidated');
      }

      return response;
    } catch (error) {
      console.error('❌ API call failed:', error);
      throw error;
    }
  }, [logout]);

  return { apiCall };
}

// Helper function for common API patterns
export function useApi() {
  const { apiCall } = useApiCall();

  const get = useCallback((url: string, options?: ApiCallOptions) => {
    return apiCall(url, { method: 'GET', ...options });
  }, [apiCall]);

  const post = useCallback((url: string, data?: any, options?: ApiCallOptions) => {
    return apiCall(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers
      },
      body: data ? JSON.stringify(data) : undefined,
      ...options
    });
  }, [apiCall]);

  const put = useCallback((url: string, data?: any, options?: ApiCallOptions) => {
    return apiCall(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers
      },
      body: data ? JSON.stringify(data) : undefined,
      ...options
    });
  }, [apiCall]);

  const del = useCallback((url: string, options?: ApiCallOptions) => {
    return apiCall(url, { method: 'DELETE', ...options });
  }, [apiCall]);

  return { get, post, put, delete: del, apiCall };
}
