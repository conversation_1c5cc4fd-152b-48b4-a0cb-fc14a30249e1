// src/app/api/autofill/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getMostRecentCredential, getSavedCredentials, getSession } from '@/lib/session';
import { getIronSession } from 'iron-session';
import { sessionOptions, type SessionData } from '@/lib/session';

// GET /api/autofill - ดึงข้อมูล autofill ล่าสุด
export async function GET(req: NextRequest) {
  try {
    console.log('🔑 Autofill API called');

    const mostRecentCredential = await getMostRecentCredential(req);

    if (mostRecentCredential) {
      console.log('✅ Found autofill credential for:', mostRecentCredential.email);
      return NextResponse.json({
        message: 'พบข้อมูล autofill',
        hasCredential: true,
        credential: {
          email: mostRecentCredential.email,
          password: mostRecentCredential.password,
          displayName: mostRecentCredential.displayName
        }
      }, { status: 200 });
    } else {
      console.log('ℹ️ No autofill credentials found');
      return NextResponse.json({
        message: 'ไม่พบข้อมูล autofill',
        hasCredential: false,
        credential: null
      }, { status: 200 });
    }

  } catch (error: any) {
    console.error('❌ Autofill error:', error.message);
    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในการดึงข้อมูล autofill',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}

// DELETE /api/autofill - ลบข้อมูล autofill
export async function DELETE(req: NextRequest) {
  try {
    console.log('🗑️ Delete autofill API called');

    const response = NextResponse.json({
      message: 'ลบข้อมูล autofill สำเร็จ'
    }, { status: 200 });

    // Clear autofill credentials but keep session
    const session = await getIronSession<SessionData>(req, response, sessionOptions);
    session.savedCredentials = [];
    await session.save();

    console.log('✅ Autofill credentials cleared');
    return response;

  } catch (error: any) {
    console.error('❌ Delete autofill error:', error.message);
    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในการลบข้อมูล autofill',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}

// POST /api/autofill - ดูรายการ autofill ทั้งหมด
export async function POST(req: NextRequest) {
  try {
    console.log('📋 List autofill API called');

    const credentials = await getSavedCredentials(req);

    return NextResponse.json({
      message: 'ดึงรายการ autofill สำเร็จ',
      credentials: credentials
    }, { status: 200 });

  } catch (error: any) {
    console.error('❌ List autofill error:', error.message);
    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในการดึงรายการ autofill',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}
