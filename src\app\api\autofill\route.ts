// src/app/api/autofill/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getMostRecentCredential, clearSavedCredentials } from '@/lib/session';

// GET /api/autofill - ดึงข้อมูล autofill ล่าสุด
export async function GET(req: NextRequest) {
  try {
    console.log('🔑 Autofill API called');

    const mostRecentCredential = await getMostRecentCredential(req);

    if (mostRecentCredential) {
      console.log('✅ Found autofill credential for:', mostRecentCredential.email);
      return NextResponse.json({
        message: 'พบข้อมูล autofill',
        hasCredential: true,
        credential: {
          email: mostRecentCredential.email,
          password: mostRecentCredential.password,
          displayName: mostRecentCredential.displayName
        }
      }, { status: 200 });
    } else {
      console.log('ℹ️ No autofill credentials found');
      return NextResponse.json({
        message: 'ไม่พบข้อมูล autofill',
        hasCredential: false,
        credential: null
      }, { status: 200 });
    }

  } catch (error: any) {
    console.error('❌ Autofill error:', error.message);
    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในการดึงข้อมูล autofill',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}

// DELETE /api/autofill - ลบข้อมูล autofill
export async function DELETE(req: NextRequest) {
  try {
    console.log('🗑️ Clear autofill API called');

    const url = new URL(req.url);
    const email = url.searchParams.get('email'); // Optional: clear specific email

    const response = NextResponse.json({
      message: email ? `ลบข้อมูล autofill สำหรับ ${email} สำเร็จ` : 'ลบข้อมูล autofill ทั้งหมดสำเร็จ'
    }, { status: 200 });

    // Clear saved credentials
    await clearSavedCredentials(req, response, email || undefined);

    console.log('✅ Autofill credentials cleared:', email || 'all');
    return response;

  } catch (error: any) {
    console.error('❌ Clear autofill error:', error.message);
    return NextResponse.json({
      error: 'เกิดข้อผิดพลาดในการลบข้อมูล autofill',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}
