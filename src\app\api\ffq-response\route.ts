import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { userId, answers } = body; // answers: { [questionId]: { frequency, portion? } }
    if (!userId || !answers || typeof answers !== 'object') {
      return NextResponse.json({ error: 'Missing userId or answers' }, { status: 400 });
    }

    // ลบคำตอบเดิมของ userId นี้ (ถ้ามี)
    await prisma.fFQResponse.deleteMany({ where: { userId } });

    // เตรียมข้อมูลใหม่
    const data = Object.entries(answers).map(([questionId, value]: any) => ({
      userId: Number(userId),
      questionId: Number(questionId),
      frequency: typeof value === 'object' && value.frequency ? value.frequency : String(value),
      portion: value.portion || null,
    }));

    // บันทึกข้อมูลใหม่ทั้งหมด
    await prisma.fFQResponse.createMany({ data });
    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to save FFQ responses' }, { status: 500 });
  }
}
