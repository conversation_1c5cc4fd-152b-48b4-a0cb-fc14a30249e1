import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET() {
  try {
    // ดึงข้อมูล FFQResponse ทั้งหมด พร้อม user และ question
    const responses = await prisma.fFQResponse.findMany({
      include: {
        user: true,
        question: true,
      },
      orderBy: [{ userId: 'asc' }, { questionId: 'asc' }],
    });

    // รวมข้อมูลภาพรวม (summary)
    const summary = {} as Record<string, Record<string, number>>; // { questionId: { [frequency]: count } }
    responses.forEach(r => {
      const qid = r.questionId + '';
      const freq = r.frequency;
      if (!summary[qid]) summary[qid] = {};
      summary[qid][freq] = (summary[qid][freq] || 0) + 1;
    });

    return NextResponse.json({ responses, summary });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch FFQ report' }, { status: 500 });
  }
}
