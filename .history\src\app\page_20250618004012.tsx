"use client";

import { useState, useEffect } from "react";
import { initializeAuth, clearStoredUser, getStoredUser, type StoredUser } from "@/lib/auth-utils";
import LogoutConfirmModal from "@/components/LogoutConfirmModal";
import {
  User, Calendar, Target, TrendingUp, Plus, Camera,
  Utensils, Apple, Coffee, Clock, Award, Bell, Settings,
  BarChart3, PieChart, Activity, Heart, Zap, Scale,
  BookOpen, CheckCircle, AlertCircle, ChevronRight,
  Sunrise, Sun, Sunset, Moon, Upload, Edit3
} from "lucide-react";

export default function NutritionHomeDashboard() {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [todaysMeals, setTodaysMeals] = useState([
    {
      id: 1,
      type: "breakfast",
      time: "07:30",
      foods: ["ข้าวต้มกุ้ง", "ไข่ดาว", "กาแฟดำ"],
      calories: 450,
      status: "verified",
      beforeImage: "📸",
      afterImage: "📸"
    },
    {
      id: 2,
      type: "lunch",
      time: "12:00",
      foods: ["ข้าวผัดกุ้ง", "ต้มยำกุ้ง", "น้ำเปล่า"],
      calories: 680,
      status: "pending",
      beforeImage: "📸",
      afterImage: null
    }
  ]);

  // User data from localStorage or default
  const [userData, setUserData] = useState({
    name: "ผู้ใช้งาน",
    age: 25,
    weight: 70,
    height: 175,
    bmi: 22.9,
    goal: "health",
    dailyCalories: 2200,
    consumedCalories: 1130,
    remainingCalories: 1070
  });

  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [sessionInfo, setSessionInfo] = useState(null);
  const [loginHistory, setLoginHistory] = useState([]);
  const [isLoadingUser, setIsLoadingUser] = useState(true);

  // Initialize session-based authentication
  useEffect(() => {
    const checkSession = async () => {
      // ตรวจสอบ preloaded data จาก login ก่อน
      const tempUserData = sessionStorage.getItem('tempUserData');
      if (tempUserData) {
        try {
          const preloadedData = JSON.parse(tempUserData);
          setUserData(preloadedData);
          setIsLoggedIn(true);
          setIsLoadingUser(false);
          sessionStorage.removeItem('tempUserData'); // ลบ temp data
          console.log('✅ Using preloaded user data:', preloadedData.name);
          return; // ออกจากฟังก์ชันเพราะมี preloaded data แล้ว
        } catch (error) {
          console.error('Error parsing preloaded data:', error);
        }
      }

      setIsLoadingUser(true);

      try {
        const response = await fetch('/api/session', {
          method: 'GET',
          credentials: 'include' // Include cookies
        });

        if (response.ok) {
          const data = await response.json();

          if (data.currentSession && data.currentSession.user) {
            const user = data.currentSession.user;

            // Update user data แบบ batch เพื่อ<|im_start|>ความเร็ว
            const newUserData = {
              name: user.fullName || "ผู้ใช้งาน",
              age: user.age || 30,
              weight: user.weight || 70,
              height: user.height || 175,
              bmi: user.bmi || 22.9,
              goal: user.goal || "health",
              dailyCalories: user.tdee || 2200,
              consumedCalories: 1130,
              remainingCalories: (user.tdee || 2200) - 1130
            };

            // Update states แบบ batch
            setUserData(newUserData);
            setIsLoggedIn(true);
            setSessionInfo(data.currentSession);
            setLoginHistory(data.loginHistory || []);

            console.log('✅ Session authenticated:', user.fullName);
          } else {
            setIsLoggedIn(false);
            console.log('❌ No active session');
          }
        } else {
          setIsLoggedIn(false);
          console.log('❌ Session check failed');
          // Redirect to login page if no session
          window.location.href = '/login';
          return;
        }
      } catch (error) {
        console.error('❌ Session check error:', error);
        setIsLoggedIn(false);
        // Redirect to login page on error
        window.location.href = '/login';
        return;
      } finally {
        setIsLoadingUser(false);
      }
    };

    checkSession();
  }, []);

  // Logout function
  const handleLogout = async () => {
    try {
      await fetch('/api/login', {
        method: 'DELETE',
        credentials: 'include' // Include cookies
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear stored user data
      clearStoredUser();
      setIsLoggedIn(false);
      setSessionInfo(null);
      setLoginHistory([]);
      // Redirect to login
      window.location.href = '/login';
    }
  };

  // Meal types configuration
  const mealTypes = [
    { type: "breakfast", label: "มื้อเช้า", icon: Sunrise, color: "from-orange-400 to-amber-500", time: "06:00-10:00" },
    { type: "lunch", label: "มื้อกลางวัน", icon: Sun, color: "from-yellow-400 to-orange-500", time: "11:00-14:00" },
    { type: "dinner", label: "มื้อเย็น", icon: Sunset, color: "from-purple-400 to-pink-500", time: "17:00-20:00" },
    { type: "snack", label: "ของว่าง", icon: Coffee, color: "from-blue-400 to-cyan-500", time: "ตลอดวัน" }
  ];

  const handleAddMeal = (mealType: string) => {
    console.log(`เพิ่มมื้อ: ${mealType}`);
    // Navigate to food logging page
  };

  const getMealIcon = (type: string) => {
    const meal = mealTypes.find(m => m.type === type);
    return meal ? meal.icon : Utensils;
  };

  const getMealLabel = (type: string) => {
    const meal = mealTypes.find(m => m.type === type);
    return meal ? meal.label : type;
  };

  // Show loading screen while checking session
  if (isLoadingUser) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-3xl flex items-center justify-center mx-auto mb-4 shadow-lg">
            <Utensils className="w-8 h-8 text-white" />
          </div>
          <div className="w-8 h-8 border-4 border-emerald-200 border-t-emerald-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">กำลังตรวจสอบการเข้าสู่ระบบ...</p>
        </div>
      </div>
    );
  }

  // Don't render main content if not logged in (will redirect)
  if (!isLoggedIn) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-3xl flex items-center justify-center mx-auto mb-4 shadow-lg">
            <Utensils className="w-8 h-8 text-white" />
          </div>
          <p className="text-gray-600">กำลังนำทางไปหน้าเข้าสู่ระบบ...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Utensils className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  ระบบบันทึกโภชนาการ
                </h1>
                <div className="text-sm text-gray-600">
                  สวัสดี{" "}
                  {isLoadingUser ? (
                    <span className="inline-block w-20 h-4 bg-gray-200 rounded animate-pulse"></span>
                  ) : (
                    <span className="transition-all duration-500 ease-out transform opacity-0 animate-fade-in">
                      {userData.name}
                    </span>
                  )}{" "}
                  <span className={`transition-all duration-300 ${isLoadingUser ? 'opacity-50' : 'opacity-100'}`}>
                    👋
                  </span>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <button className="p-2 hover:bg-gray-100 rounded-xl transition-colors">
                <Bell className="w-5 h-5 text-gray-600" />
              </button>
              <button className="p-2 hover:bg-gray-100 rounded-xl transition-colors">
                <Settings className="w-5 h-5 text-gray-600" />
              </button>

              {isLoggedIn ? (
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-xl flex items-center justify-center">
                    <User className="w-5 h-5 text-white" />
                  </div>
                  <button
                    onClick={handleLogout}
                    className="px-4 py-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-xl transition-colors"
                  >
                    ออกจากระบบ
                  </button>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <a
                    href="/login"
                    className="px-4 py-2 text-sm text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50 rounded-xl transition-colors"
                  >
                    เข้าสู่ระบบ
                  </a>
                  <a
                    href="/register"
                    className="px-4 py-2 text-sm bg-emerald-500 text-white hover:bg-emerald-600 rounded-xl transition-colors"
                  >
                    สมัครสมาชิก
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-8">
            
            {/* Daily Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Calories Card */}
              <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-6 border border-white/50 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-xl flex items-center justify-center">
                    <Zap className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-sm text-gray-500">วันนี้</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-1">
                  {userData.consumedCalories.toLocaleString()}
                </h3>
                <p className="text-sm text-gray-600 mb-3">
                  จาก {userData.dailyCalories.toLocaleString()} แคลอรี่
                </p>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-emerald-400 to-teal-500 h-2 rounded-full transition-all duration-500"
                    style={{ width: `${(userData.consumedCalories / userData.dailyCalories) * 100}%` }}
                  />
                </div>
              </div>

              {/* BMI Card */}
              <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-6 border border-white/50 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-xl flex items-center justify-center">
                    <Scale className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-sm text-gray-500">BMI</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-1">
                  {userData.bmi}
                </h3>
                <p className="text-sm text-emerald-600 font-medium">
                  น้ำหนักปกติ
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {userData.weight} กก. / {userData.height} ซม.
                </p>
              </div>

              {/* Goal Card */}
              <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-6 border border-white/50 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-xl flex items-center justify-center">
                    <Target className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-sm text-gray-500">เป้าหมาย</span>
                </div>
                <h3 className="text-lg font-bold text-gray-800 mb-1">
                  สุขภาพดีขึ้น
                </h3>
                <p className="text-sm text-gray-600">
                  กำลังดำเนินการ
                </p>
                <div className="flex items-center mt-2">
                  <CheckCircle className="w-4 h-4 text-emerald-500 mr-2" />
                  <span className="text-xs text-emerald-600">อยู่ในเป้า</span>
                </div>
              </div>
            </div>

            {/* Date Selector */}
            <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-6 border border-white/50 shadow-lg">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <Calendar className="w-6 h-6 text-emerald-600" />
                  <h2 className="text-xl font-bold text-gray-800">บันทึกอาหารวันนี้</h2>
                </div>
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="px-4 py-2 border border-gray-200 rounded-xl focus:border-emerald-400 focus:ring-2 focus:ring-emerald-100 transition-colors"
                />
              </div>

              {/* Meal Sections */}
              <div className="space-y-6">
                {mealTypes.map((mealType) => {
                  const Icon = mealType.icon;
                  const mealsOfType = todaysMeals.filter(meal => meal.type === mealType.type);
                  
                  return (
                    <div key={mealType.type} className="border border-gray-200 rounded-2xl p-6 bg-gray-50/50">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className={`w-10 h-10 bg-gradient-to-r ${mealType.color} rounded-xl flex items-center justify-center shadow-md`}>
                            <Icon className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-800">{mealType.label}</h3>
                            <p className="text-sm text-gray-500">{mealType.time}</p>
                          </div>
                        </div>
                        <button 
                          onClick={() => handleAddMeal(mealType.type)}
                          className="flex items-center gap-2 px-4 py-2 bg-emerald-500 text-white rounded-xl hover:bg-emerald-600 transition-colors shadow-md"
                        >
                          <Plus className="w-4 h-4" />
                          <span className="text-sm font-medium">เพิ่ม</span>
                        </button>
                      </div>

                      {/* Meal Entries */}
                      {mealsOfType.length > 0 ? (
                        <div className="space-y-3">
                          {mealsOfType.map((meal) => (
                            <div key={meal.id} className="bg-white rounded-xl p-4 border border-gray-100 shadow-sm">
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center gap-3 mb-2">
                                    <Clock className="w-4 h-4 text-gray-400" />
                                    <span className="text-sm font-medium text-gray-700">{meal.time}</span>
                                    {meal.status === 'verified' ? (
                                      <CheckCircle className="w-4 h-4 text-emerald-500" />
                                    ) : (
                                      <AlertCircle className="w-4 h-4 text-amber-500" />
                                    )}
                                  </div>
                                  <div className="flex flex-wrap gap-2 mb-2">
                                    {meal.foods.map((food, index) => (
                                      <span key={index} className="px-2 py-1 bg-emerald-50 text-emerald-700 rounded-lg text-xs font-medium">
                                        {food}
                                      </span>
                                    ))}
                                  </div>
                                  <p className="text-sm text-gray-600">
                                    <span className="font-medium">{meal.calories}</span> แคลอรี่
                                  </p>
                                </div>
                                <div className="flex items-center gap-2 ml-4">
                                  <div className="flex items-center gap-1">
                                    <span className="text-xs text-gray-500">ก่อน:</span>
                                    <span className="text-sm">{meal.beforeImage}</span>
                                  </div>
                                  {meal.afterImage && (
                                    <div className="flex items-center gap-1">
                                      <span className="text-xs text-gray-500">หลัง:</span>
                                      <span className="text-sm">{meal.afterImage}</span>
                                    </div>
                                  )}
                                  <button className="p-1 hover:bg-gray-100 rounded-lg transition-colors">
                                    <Edit3 className="w-4 h-4 text-gray-400" />
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <Utensils className="w-8 h-8 mx-auto mb-2 opacity-50" />
                          <p>ยังไม่มีการบันทึกอาหาร{mealType.label}</p>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Right Column - Sidebar */}
          <div className="space-y-6">
            
            {/* Quick Actions */}
            <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-6 border border-white/50 shadow-lg">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                <Zap className="w-5 h-5 text-emerald-600" />
                ดำเนินการด่วน
              </h3>
              <div className="space-y-3">
                <button className="w-full flex items-center gap-3 p-4 bg-emerald-50 hover:bg-emerald-100 rounded-xl transition-colors group">
                  <Camera className="w-5 h-5 text-emerald-600" />
                  <span className="font-medium text-emerald-700">ถ่ายรูปอาหาร</span>
                  <ChevronRight className="w-4 h-4 text-emerald-600 ml-auto group-hover:translate-x-1 transition-transform" />
                </button>
                
                <button className="w-full flex items-center gap-3 p-4 bg-blue-50 hover:bg-blue-100 rounded-xl transition-colors group">
                  <Plus className="w-5 h-5 text-blue-600" />
                  <span className="font-medium text-blue-700">เพิ่มอาหารด่วน</span>
                  <ChevronRight className="w-4 h-4 text-blue-600 ml-auto group-hover:translate-x-1 transition-transform" />
                </button>
                
                <button className="w-full flex items-center gap-3 p-4 bg-purple-50 hover:bg-purple-100 rounded-xl transition-colors group">
                  <BarChart3 className="w-5 h-5 text-purple-600" />
                  <span className="font-medium text-purple-700">ดูรายงาน</span>
                  <ChevronRight className="w-4 h-4 text-purple-600 ml-auto group-hover:translate-x-1 transition-transform" />
                </button>
              </div>
            </div>

            {/* Weekly Progress */}
            <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-6 border border-white/50 shadow-lg">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-emerald-600" />
                ความคืบหน้าสัปดาห์นี้
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">บันทึกครบทุกวัน</span>
                  <div className="flex items-center gap-1">
                    <span className="text-sm font-bold text-emerald-600">5/7</span>
                    <span className="text-xs text-gray-500">วัน</span>
                  </div>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-gradient-to-r from-emerald-400 to-teal-500 h-2 rounded-full w-5/7"></div>
                </div>
                
                <div className="grid grid-cols-7 gap-1 mt-4">
                  {['จ', 'อ', 'พ', 'พฤ', 'ศ', 'ส', 'อา'].map((day, index) => (
                    <div key={day} className="text-center">
                      <div className="text-xs text-gray-500 mb-1">{day}</div>
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                        index < 5 ? 'bg-emerald-100 text-emerald-600' : 'bg-gray-100 text-gray-400'
                      }`}>
                        {index < 5 ? <CheckCircle className="w-4 h-4" /> : <span className="w-2 h-2 bg-gray-300 rounded-full" />}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Nutrition Tips */}
            <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-6 border border-white/50 shadow-lg">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
                <BookOpen className="w-5 h-5 text-emerald-600" />
                เทคนิคโภชนาการ
              </h3>
              
              <div className="space-y-4">
                <div className="p-4 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl border border-emerald-100">
                  <div className="flex items-start gap-3">
                    <Apple className="w-5 h-5 text-emerald-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-emerald-800 mb-1">ผักผลไม้ 5 สี</h4>
                      <p className="text-sm text-emerald-700">ทานผักผลไม้หลากสีเพื่อรับวิตามินครบถ้วน</p>
                    </div>
                  </div>
                </div>
                
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                  <div className="flex items-start gap-3">
                    <Heart className="w-5 h-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-800 mb-1">ดื่มน้ำเพียงพอ</h4>
                      <p className="text-sm text-blue-700">ดื่มน้ำอย่างน้อย 8 แก้วต่อวัน</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Achievement Badge */}
            <div className="bg-gradient-to-br from-amber-50 to-orange-50 border border-amber-200 rounded-3xl p-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                  <Award className="w-8 h-8 text-white" />
                </div>
                <h3 className="font-bold text-amber-800 mb-2">เซียนบันทึกอาหาร!</h3>
                <p className="text-sm text-amber-700">บันทึกอาหารต่อเนื่อง 5 วันแล้ว</p>
                <div className="mt-3">
                  <span className="px-3 py-1 bg-amber-200 text-amber-800 rounded-full text-xs font-medium">
                    🔥 5 วันติดต่อกัน
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}