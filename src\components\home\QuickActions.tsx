// C:\SofterM Github\Project-nutrition\src\components\QuickActions.tsx
"use client";

import { Zap, Camera, Plus, BarChart3, ChevronRight } from "lucide-react";

export default function QuickActions() {
  return (
    <div className="bg-white/70 backdrop-blur-lg rounded-3xl p-6 border border-white/50 shadow-lg">
      <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
        <Zap className="w-5 h-5 text-emerald-600" />
        ดำเนินการด่วน
      </h3>
      <div className="space-y-3">
        <button className="w-full flex items-center gap-3 p-4 bg-emerald-50 hover:bg-emerald-100 rounded-xl transition-colors group">
          <Camera className="w-5 h-5 text-emerald-600" />
          <span className="font-medium text-emerald-700">ถ่ายรูปอาหาร</span>
          <ChevronRight className="w-4 h-4 text-emerald-600 ml-auto group-hover:translate-x-1 transition-transform" />
        </button>
        
        <button className="w-full flex items-center gap-3 p-4 bg-blue-50 hover:bg-blue-100 rounded-xl transition-colors group">
          <Plus className="w-5 h-5 text-blue-600" />
          <span className="font-medium text-blue-700">เพิ่มอาหารด่วน</span>
          <ChevronRight className="w-4 h-4 text-blue-600 ml-auto group-hover:translate-x-1 transition-transform" />
        </button>
        
        <button className="w-full flex items-center gap-3 p-4 bg-purple-50 hover:bg-purple-100 rounded-xl transition-colors group">
          <BarChart3 className="w-5 h-5 text-purple-600" />
          <span className="font-medium text-purple-700">ดูรายงาน</span>
          <ChevronRight className="w-4 h-4 text-purple-600 ml-auto group-hover:translate-x-1 transition-transform" />
        </button>
      </div>
    </div>
  );
}