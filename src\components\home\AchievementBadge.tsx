// C:\SofterM Github\Project-nutrition\src\components\AchievementBadge.tsx
"use client";

import { Award, Flame, Star } from "lucide-react";

export default function AchievementBadge() {
  return (
    <div className="bg-gradient-to-br from-amber-50 to-orange-50 border border-amber-200 rounded-3xl p-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
          <Award className="w-8 h-8 text-white" />
        </div>
        <h3 className="font-bold text-amber-800 mb-2">เซียนบันทึกอาหาร!</h3>
        <p className="text-sm text-amber-700">บันทึกอาหารต่อเนื่อง 5 วันแล้ว</p>
        <div className="mt-3">
          <span className="inline-flex items-center gap-1 px-3 py-1 bg-amber-200 text-amber-800 rounded-full text-xs font-medium">
            <Flame className="w-3 h-3" />
            5 วันติดต่อกัน
          </span>
        </div>
      </div>
    </div>
  );
}