// src/components/AuthProvider.tsx
"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { initializeAuth, autoRefreshToken, clearStoredUser, type StoredUser } from '@/lib/auth-utils';
import SessionExpiredModal from './SessionExpiredModal';

interface AuthContextType {
  user: StoredUser | null;
  isLoggedIn: boolean;
  isLoading: boolean;
  login: (user: StoredUser, token: string, rememberMe?: boolean) => void;
  logout: () => void;
  refreshToken: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<StoredUser | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showSessionExpired, setShowSessionExpired] = useState(false);

  // Initialize session-based auth on mount
  useEffect(() => {
    const initAuth = async () => {
      try {
        // Check session-based authentication first
        const sessionResponse = await fetch('/api/session', {
          method: 'GET',
          credentials: 'include'
        });

        if (sessionResponse.ok) {
          const data = await sessionResponse.json();

          if (data.currentSession && data.currentSession.user) {
            const user = data.currentSession.user;

            // Convert session user to StoredUser format
            const storedUser: StoredUser = {
              id: user.id,
              email: user.email,
              fullName: user.fullName,
              gender: user.gender,
              height: user.height,
              weight: user.weight,
              activityLevel: user.activityLevel,
              goal: user.goal,
              foodAllergies: user.foodAllergies,
              bmr: user.bmr,
              tdee: user.tdee,
              role: user.role,
              bmi: user.bmi,
              age: user.age
            };

            setUser(storedUser);
            setIsLoggedIn(true);
            console.log('✅ Session-based auth initialized:', user.fullName);
          } else {
            setUser(null);
            setIsLoggedIn(false);
            console.log('❌ No active session');
          }
        } else {
          // Fallback to JWT-based auth for backward compatibility
          const authenticatedUser = await initializeAuth();

          if (authenticatedUser) {
            setUser(authenticatedUser);
            setIsLoggedIn(true);
            console.log('✅ JWT-based auth initialized:', authenticatedUser.fullName);
          } else {
            setUser(null);
            setIsLoggedIn(false);
            console.log('❌ No authenticated user found');
          }
        }
      } catch (error) {
        console.error('❌ Auth initialization failed:', error);
        setUser(null);
        setIsLoggedIn(false);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  // Session monitoring - check session validity every 30 seconds
  useEffect(() => {
    if (!isLoggedIn) return;

    const sessionCheckInterval = setInterval(async () => {
      try {
        const sessionResponse = await fetch('/api/session', {
          method: 'GET',
          credentials: 'include'
        });

        if (!sessionResponse.ok) {
          // Session is invalid (401) - user was forced logout from another device
          console.log('🚨 Session invalidated - forced logout detected');
          setUser(null);
          setIsLoggedIn(false);
          clearStoredUser();

          // Show session expired modal
          setShowSessionExpired(true);
        }
      } catch (error) {
        console.error('❌ Session check failed:', error);
      }
    }, 30 * 1000); // Check every 30 seconds

    // Auto-refresh token every 30 minutes if remember me is enabled
    const tokenRefreshInterval = setInterval(async () => {
      // Only auto-refresh if remember me is enabled (localStorage only)
      const rememberMe = localStorage.getItem('rememberMe') === 'true';

      if (rememberMe) {
        console.log('🔄 Attempting auto token refresh...');
        const refreshed = await autoRefreshToken();

        if (!refreshed) {
          console.log('❌ Auto refresh failed, logging out');
          logout();
        } else {
          console.log('✅ Token auto-refreshed successfully');
        }
      } else {
        console.log('⏭️ Auto refresh skipped: Session-only login');
      }
    }, 30 * 60 * 1000); // 30 minutes

    return () => {
      clearInterval(sessionCheckInterval);
      clearInterval(tokenRefreshInterval);
    };
  }, [isLoggedIn]);

  const login = (userData: StoredUser, token: string, rememberMe: boolean = false) => {
    setUser(userData);
    setIsLoggedIn(true);

    if (rememberMe) {
      // Store in localStorage for persistent login
      localStorage.setItem('user', JSON.stringify(userData));
      localStorage.setItem('accessToken', token);
      localStorage.setItem('rememberMe', 'true');
      console.log('✅ User logged in with remember me:', userData.fullName);
    } else {
      // Store in sessionStorage for session-only login
      sessionStorage.setItem('user', JSON.stringify(userData));
      sessionStorage.setItem('accessToken', token);
      sessionStorage.setItem('rememberMe', 'false');

      // Clear any existing localStorage data
      localStorage.removeItem('user');
      localStorage.removeItem('accessToken');
      localStorage.removeItem('rememberMe');

      console.log('✅ User logged in (session only):', userData.fullName);
    }
  };

  const logout = async () => {
    try {
      // Call logout API
      await fetch('/api/login', {
        method: 'DELETE',
        credentials: 'include' // Include cookies for session
      });
    } catch (error) {
      console.error('❌ Logout API error:', error);
    } finally {
      // Clear state and storage
      setUser(null);
      setIsLoggedIn(false);
      clearStoredUser();

      console.log('✅ User logged out');
    }
  };

  const refreshToken = async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.accessToken) {
          localStorage.setItem('accessToken', data.accessToken);
          console.log('✅ Token refreshed manually');
          return true;
        }
      }
    } catch (error) {
      console.error('❌ Manual token refresh failed:', error);
    }
    
    return false;
  };

  const value: AuthContextType = {
    user,
    isLoggedIn,
    isLoading,
    login,
    logout,
    refreshToken
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
