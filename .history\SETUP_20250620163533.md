# 🚀 Setup Guide - Project Nutrition

คำแนะนำการติดตั้งและตั้งค่าโปรเจคแบบละเอียด

## 📋 Prerequisites

### System Requirements
- **Node.js**: 18.18.0 หรือใหม่กว่า
- **npm**: 9.0.0 หรือใหม่กว่า (หรือ yarn, pnpm, bun)
- **Git**: สำหรับ version control
- **PostgreSQL**: 13 หรือใหม่กว่า (หรือใช้ cloud database)

### Recommended Tools
- **VS Code** - พร้อม extensions:
  - Prisma
  - Tailwind CSS IntelliSense
  - TypeScript Importer
  - ESLint
- **Prisma Studio** - Database GUI
- **Postman** - API testing

## 🛠️ Installation Steps

### 1. Clone Repository
```bash
git clone <repository-url>
cd project-nutrition
```

### 2. Install Dependencies
```bash
# ใช้ npm
npm install

# หรือ yarn
yarn install

# หรือ pnpm
pnpm install
```

### 3. Environment Setup



```

### 4. Database Setup

#### Option A: Neon DB (แนะนำ)
1. ไปที่ [Neon.tech](https://neon.tech)
2. สร้าง account และ database
3. คัดลอก connection string ใส่ใน `DATABASE_URL`

#### Option B: Local PostgreSQL
```bash
# ติดตั้ง PostgreSQL
# macOS
brew install postgresql

# Ubuntu
sudo apt-get install postgresql

# สร้าง database
createdb nutrition_db

# ตั้งค่า connection string
DATABASE_URL="postgresql://username:password@localhost:5432/nutrition_db"
```

### 5. Prisma Setup
```bash
# Generate Prisma client
npx prisma generate

# Push schema to database
npx prisma db push

# (Optional) Open Prisma Studio
npx prisma studio
```

### 6. Run Development Server
```bash
npm run dev
```

เปิด [http://localhost:3000](http://localhost:3000)

## 🔧 Configuration Details

### Environment Variables

#### Required Variables
```env
DATABASE_URL="postgresql://..."     # Database connection
SESSION_SECRET="32+ characters"     # Session encryption key
```

#### Optional Variables
```env
JWT_SECRET="32+ characters"         # JWT token secret
JWT_EXPIRES_IN="7d"                # JWT expiration
SESSION_MAX_AGE="2592000000"       # Session duration (30 days)
ENCRYPTION_KEY="32+ characters"     # Autofill encryption
RATE_LIMIT_MAX="100"               # Rate limit max requests
RATE_LIMIT_WINDOW="15"             # Rate limit window (minutes)
```

### Database Schema

#### User Table
```sql
model User {
  id            Int      @id @default(autoincrement())
  email         String   @unique
  password      String
  fullName      String
  gender        String
  birthDate     DateTime
  height        Float
  weight        Float
  activityLevel String
  goal          String
  foodAllergies String?
  bmr           Float?
  tdee          Float?
  role          String   @default("user")
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}
```

## 🧪 Testing Setup

### Manual Testing
1. **Registration**: ไปที่ `/register`
2. **Login**: ไปที่ `/login`
3. **Home**: ตรวจสอบ dashboard
4. **Logout**: ทดสอบ logout modal

### API Testing
```bash
# Test registration
curl -X POST http://localhost:3000/api/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","fullName":"Test User",...}'

# Test login
curl -X POST http://localhost:3000/api/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Database Connection Error
```
Error: Can't reach database server
```
**Solution:**
- ตรวจสอบ `DATABASE_URL` ใน .env
- ตรวจสอบ database server ทำงานหรือไม่
- ตรวจสอบ network connectivity

#### 2. Prisma Client Error
```
Error: Prisma Client is not generated
```
**Solution:**
```bash
npx prisma generate
```

#### 3. Session Secret Error
```
Error: Session secret is required
```
**Solution:**
- เพิ่ม `SESSION_SECRET` ใน .env
- ต้องมีอย่างน้อย 32 ตัวอักษร

#### 4. Port Already in Use
```
Error: Port 3000 is already in use
```
**Solution:**
```bash
# ใช้ port อื่น
npm run dev -- -p 3001

# หรือ kill process ที่ใช้ port 3000
lsof -ti:3000 | xargs kill
```

### Debug Mode
```bash
# เปิด debug logs
DEBUG=* npm run dev

# ดู Prisma queries
DATABASE_URL="postgresql://...?log=query" npm run dev
```

## 📦 Production Deployment

### Vercel (แนะนำ)
1. Push code ไป GitHub
2. Connect repository ใน Vercel
3. ตั้งค่า Environment Variables:
   ```
   NODE_ENV=production
   DATABASE_URL=your_production_db_url
   SESSION_SECRET=your_production_secret
   FRONTEND_URL=https://your-domain.com
   ```
4. Deploy!

### Manual Deployment
```bash
# Build application
npm run build

# Start production server
npm start
```

## 🔒 Security Checklist

- [ ] `SESSION_SECRET` มีความยาวอย่างน้อย 32 ตัวอักษร
- [ ] `DATABASE_URL` ไม่ถูก commit ใน git
- [ ] Production ใช้ `NODE_ENV=production`
- [ ] Database มี SSL enabled
- [ ] Rate limiting ถูกเปิดใช้งาน
- [ ] CORS ถูกตั้งค่าอย่างถูกต้อง

## 📞 Support

หากมีปัญหา:
1. ตรวจสอบ logs ใน console
2. ดู [Troubleshooting](#-troubleshooting)
3. สร้าง Issue ใน GitHub
4. ติดต่อทีมพัฒนา

---

**หมายเหตุ**: คำแนะนำนี้อาจเปลี่ยนแปลงตามการอัพเดทของโปรเจค
