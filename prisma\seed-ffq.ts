import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const ffqQuestions = [
  // ข้อมูลทั่วไป
  {
    question: 'เพศ',
    foodGroup: 'ข้อมูลทั่วไป',
    order: 1,
    options: ['ชาย', 'หญิง', 'ไม่ระบุ'],
    type: 'single',
  },
  {
    question: 'อายุ',
    foodGroup: 'ข้อมูลทั่วไป',
    order: 2,
    options: ['ต่ำกว่า 20 ปี', '20-30 ปี', '31-40 ปี', '41-50 ปี', '51-60 ปี', 'มากกว่า 60 ปี'],
    type: 'single',
  },
  
  // หมวดข้าวและแป้ง
  {
    question: 'ข้าวสวย ข้าวหอม ข้าวเหนียว - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ข้าวและแป้ง',
    order: 3,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },
  {
    question: 'ขนมปัง โรตี ขนมปังกรอบ - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ข้าวและแป้ง',
    order: 4,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },
  {
    question: 'บะหมี่ เส้นใหญ่ เส้นเล็ก วุ้นเส้น - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ข้าวและแป้ง',
    order: 5,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },
  {
    question: 'ขนมจีน ก๋วยเตี๋ยว หมี่ฮ่องเต้ - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ข้าวและแป้ง',
    order: 6,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },

  // หมวดเนื้อสัตว์
  {
    question: 'เนื้อวัว เนื้อควาย เนื้อแพะ - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'เนื้อสัตว์',
    order: 7,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },
  {
    question: 'เนื้อหมู หมูสับ หมูยอ ไส้กรอก - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'เนื้อสัตว์',
    order: 8,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },
  {
    question: 'เนื้อไก่ เนื้อเป็ด เนื้อนก - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'เนื้อสัตว์',
    order: 9,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },
  {
    question: 'เครื่องในสัตว์ ตับ หัวใจ ไต - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'เนื้อสัตว์',
    order: 10,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },

  // หมวดปลาและอาหารทะเล
  {
    question: 'ปลาน้ำจืด ปลาทะเล ปลาแห้ง - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ปลาและอาหารทะเล',
    order: 11,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },
  {
    question: 'กุ้ง ปู หอย หมึก - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ปลาและอาหารทะเล',
    order: 12,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },
  {
    question: 'ปลาเค็ม ปลาร้า กุ้งแห้ง - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ปลาและอาหารทะเล',
    order: 13,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },

  // หมวดไข่และนม
  {
    question: 'ไข่ไก่ ไข่เป็ด ไข่นกกระทา - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ไข่และนม',
    order: 14,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },
  {
    question: 'นมสด นมผง โยเกิร์ต - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ไข่และนม',
    order: 15,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },
  {
    question: 'เนยแข็ง ครีม เนยสด - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ไข่และนม',
    order: 16,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },

  // หมวดผักใบเขียว
  {
    question: 'ผักบุ้ง ผักคะน้า ผักกาด - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ผักใบเขียว',
    order: 17,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },
  {
    question: 'กะหล่ำปลี กะหล่ำดอก บร็อกโคลี่ - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ผักใบเขียว',
    order: 18,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },
  {
    question: 'ผักโขม ผักชี ใบแมงลัก - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ผักใบเขียว',
    order: 19,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },

  // หมวดผลไม้
  {
    question: 'กล้วย ส้ม แอปเปิ้ล - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ผลไม้',
    order: 17,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },
  {
    question: 'มะม่วง สับปะรด มะละกอ - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ผลไม้',
    order: 18,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },

  // หมวดเครื่องดื่ม
  {
    question: 'น้ำอัดลม โค้ก เป๊ปซี่ - คุณดื่มบ่อยแค่ไหน?',
    foodGroup: 'เครื่องดื่ม',
    order: 19,
    options: ['ไม่เคยดื่ม', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },
  {
    question: 'ถั่วลิสง งาดำ งาขาว - คุณทานบ่อยแค่ไหน?',
    foodGroup: 'ถั่วและธัญพืช',
    order: 20,
    options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
    type: 'frequency',
  },
];

async function main() {
  // ลบข้อมูลเดิมก่อน (ถ้ามี) - ต้องลบตาม dependency order
  try {
    console.log('🗑️  Cleaning existing FFQ data...');
    
    // ลบ FFQAnswer ก่อน (ถ้ามี)
    await prisma.fFQAnswer.deleteMany();
    console.log('   - Deleted FFQAnswers');
    
    // ลบ FFQSubmission ก่อน (ถ้ามี)
    await prisma.fFQSubmission.deleteMany();
    console.log('   - Deleted FFQSubmissions');
    
    // ลบ FFQQuestion
    await prisma.fFQQuestion.deleteMany();
    console.log('   - Deleted FFQQuestions');
    
    // ลบ FFQSet สุดท้าย
    await prisma.fFQSet.deleteMany();
    console.log('   - Deleted FFQSets');
    
    console.log('✅ Cleanup completed!');
  } catch (error) {
    console.log('⚠️  Some tables might not exist yet, continuing...');
  }

  // สร้าง FFQSet หลายชุด
  const sets = await prisma.fFQSet.createMany({
    data: [
      {
        name: 'แบบสอบถาม FFQ ทั่วไป',
        description: 'แบบสอบถามความถี่การบริโภคอาหารสำหรับประชาชนทั่วไป เหมาะสำหรับผู้ใหญ่วัย 18-60 ปี',
        isActive: true,
      },
      {
        name: 'แบบสอบถาม FFQ ผู้สูงอายุ',
        description: 'แบบสอบถามความถี่การบริโภคอาหารสำหรับผู้สูงอายุ 60 ปีขึ้นไป มีการปรับเนื้อหาให้เหมาะสมกับวัย',
        isActive: true,
      },
      {
        name: 'แบบสอบถาม FFQ เด็กและวัยรุ่น',
        description: 'แบบสอบถามความถี่การบริโภคอาหารสำหรับเด็กและวัยรุ่น อายุ 10-18 ปี',
        isActive: true,
      },
      {
        name: 'แบบสอบถาม FFQ ผู้ป่วยเบาหวาน',
        description: 'แบบสอบถามเฉพาะสำหรับผู้ป่วยโรคเบาหวาน เน้นการประเมินอาหารที่มีผลต่อระดับน้ำตาล',
        isActive: true,
      },
      {
        name: 'แบบสอบถาม FFQ (ทดสอบ)',
        description: 'แบบสอบถามสำหรับทดสอบระบบ ยังไม่เปิดให้ใช้งาน',
        isActive: false,
      }
    ]
  });

  // ดึง FFQSet ที่สร้างแล้ว
  const createdSets = await prisma.fFQSet.findMany();
  
  // สร้างคำถามสำหรับแต่ละชุด
  const setQuestions = {
    'ทั่วไป': ffqQuestions.slice(0, 5), // 5 คำถามแรก
    'ผู้สูงอายุ': [
      {
        question: 'เพศ',
        foodGroup: 'ข้อมูลทั่วไป',
        order: 1,
        options: ['ชาย', 'หญิง', 'ไม่ระบุ'],
        type: 'single',
      },
      {
        question: 'อายุ',
        foodGroup: 'ข้อมูลทั่วไป',
        order: 2,
        options: ['60-65 ปี', '66-70 ปี', '71-75 ปี', '76-80 ปี', 'มากกว่า 80 ปี'],
        type: 'single',
      },
      {
        question: 'ข้าวสวย ข้าวต้ม โจ๊ก - คุณทานบ่อยแค่ไหน?',
        foodGroup: 'ข้าวและแป้ง',
        order: 3,
        options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
        type: 'frequency',
      },
      {
        question: 'เนื้อปลาต้ม ปลานึ่ง - คุณทานบ่อยแค่ไหน?',
        foodGroup: 'ปลาและอาหารทะเล',
        order: 4,
        options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
        type: 'frequency',
      },
      {
        question: 'ผักต้ม ผักลวก ต้มแกง - คุณทานบ่อยแค่ไหน?',
        foodGroup: 'ผักใบเขียว',
        order: 5,
        options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
        type: 'frequency',
      }
    ],
    'เด็กและวัยรุ่น': [
      {
        question: 'เพศ',
        foodGroup: 'ข้อมูลทั่วไป',
        order: 1,
        options: ['ชาย', 'หญิง', 'ไม่ระบุ'],
        type: 'single',
      },
      {
        question: 'อายุ',
        foodGroup: 'ข้อมูลทั่วไป',
        order: 2,
        options: ['10-12 ปี', '13-15 ปี', '16-18 ปี'],
        type: 'single',
      },
      {
        question: 'ข้าวผัด ข้าวกะเพรา ข้าวไข่เจียว - คุณทานบ่อยแค่ไหน?',
        foodGroup: 'ข้าวและแป้ง',
        order: 3,
        options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
        type: 'frequency',
      },
      {
        question: 'ไก่ทอด หมูทอด ปลาทอด - คุณทานบ่อยแค่ไหน?',
        foodGroup: 'เนื้อสัตว์',
        order: 4,
        options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
        type: 'frequency',
      },
      {
        question: 'นมสด นมรสต่างๆ นมช็อกโกแลต - คุณดื่มบ่อยแค่ไหน?',
        foodGroup: 'ไข่และนม',
        order: 5,
        options: ['ไม่เคยดื่ม', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
        type: 'frequency',
      }
    ],
    'ผู้ป่วยเบาหวาน': [
      {
        question: 'เพศ',
        foodGroup: 'ข้อมูลทั่วไป',
        order: 1,
        options: ['ชาย', 'หญิง', 'ไม่ระบุ'],
        type: 'single',
      },
      {
        question: 'อายุ',
        foodGroup: 'ข้อมูลทั่วไป',
        order: 2,
        options: ['ต่ำกว่า 30 ปี', '30-40 ปี', '41-50 ปี', '51-60 ปี', 'มากกว่า 60 ปี'],
        type: 'single',
      },
      {
        question: 'ข้าวกล้อง ข้าวซ้อมมือ ธัญพืชเต็มเมล็ด - คุณทานบ่อยแค่ไหน?',
        foodGroup: 'ข้าวและแป้ง',
        order: 3,
        options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
        type: 'frequency',
      },
      {
        question: 'ผลไม้หวานน้อย แอปเปิ้ล ฝรั่ง - คุณทานบ่อยแค่ไหน?',
        foodGroup: 'ผลไม้',
        order: 4,
        options: ['ไม่เคยทาน', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
        type: 'frequency',
      },
      {
        question: 'เครื่องดื่มไม่มีน้ำตาล น้ำเปล่า ชาไม่หวาน - คุณดื่มบ่อยแค่ไหน?',
        foodGroup: 'เครื่องดื่ม',
        order: 5,
        options: ['ไม่เคยดื่ม', '1-3 ครั้ง/เดือน', '1-2 ครั้ง/สัปดาห์', '3-6 ครั้ง/สัปดาห์', '1 ครั้ง/วัน', '2-3 ครั้ง/วัน', 'มากกว่า 3 ครั้ง/วัน'],
        type: 'frequency',
      }
    ]
  };

  // เพิ่มคำถามสำหรับแต่ละชุด
  for (const set of createdSets) {
    let questionsToAdd: any[] = [];
    
    if (set.name.includes('ทั่วไป')) {
      questionsToAdd = setQuestions['ทั่วไป'];
    } else if (set.name.includes('ผู้สูงอายุ')) {
      questionsToAdd = setQuestions['ผู้สูงอายุ'];
    } else if (set.name.includes('เด็กและวัยรุ่น')) {
      questionsToAdd = setQuestions['เด็กและวัยรุ่น'];
    } else if (set.name.includes('ผู้ป่วยเบาหวาน')) {
      questionsToAdd = setQuestions['ผู้ป่วยเบาหวาน'];
    }

    if (questionsToAdd.length > 0) {
      const questionsWithSetId = questionsToAdd.map(q => ({
        ...q,
        ffqSetId: set.id,
      }));

      await prisma.fFQQuestion.createMany({
        data: questionsWithSetId,
        skipDuplicates: true,
      });
      
      console.log(`📝 Added ${questionsWithSetId.length} questions for ${set.name}`);
    }
  }

  console.log('✅ FFQ sets and questions seeded successfully!');
  console.log(`📋 Created ${createdSets.length} FFQ sets`);
  console.log(`❓ Created ${ffqQuestions.length} questions for general set`);
}

main()
  .catch((e) => {
    console.error('❌ Error seeding FFQ data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });