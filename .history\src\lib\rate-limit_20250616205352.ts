// src/lib/rate-limit.ts
import { NextRequest } from 'next/server';

// Simple in-memory rate limiting (for development)
// In production, use Redis or external service
interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

const store: RateLimitStore = {};

// Clean up expired entries every 5 minutes
setInterval(() => {
  const now = Date.now();
  Object.keys(store).forEach(key => {
    if (store[key].resetTime < now) {
      delete store[key];
    }
  });
}, 5 * 60 * 1000);

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Max requests per window
  message?: string; // Custom error message
}

export interface RateLimitResult {
  success: boolean;
  limit: number;
  remaining: number;
  resetTime: number;
  message?: string;
}

/**
 * Rate limiting function
 */
export function rateLimit(config: RateLimitConfig) {
  return (identifier: string): RateLimitResult => {
    const now = Date.now();
    const windowStart = now - config.windowMs;
    
    // Get or create entry
    if (!store[identifier] || store[identifier].resetTime < now) {
      store[identifier] = {
        count: 0,
        resetTime: now + config.windowMs
      };
    }
    
    const entry = store[identifier];
    
    // Check if within rate limit
    if (entry.count >= config.maxRequests) {
      return {
        success: false,
        limit: config.maxRequests,
        remaining: 0,
        resetTime: entry.resetTime,
        message: config.message || 'Too many requests'
      };
    }
    
    // Increment counter
    entry.count++;
    
    return {
      success: true,
      limit: config.maxRequests,
      remaining: config.maxRequests - entry.count,
      resetTime: entry.resetTime
    };
  };
}

/**
 * Get client identifier from request
 */
export function getClientIdentifier(req: NextRequest, includeUserAgent: boolean = false): string {
  // Get IP address
  const forwarded = req.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0].trim() : 
             req.headers.get('x-real-ip') || 
             req.ip || 
             'unknown';
  
  if (includeUserAgent) {
    const userAgent = req.headers.get('user-agent') || 'unknown';
    return `${ip}:${userAgent}`;
  }
  
  return ip;
}

/**
 * Rate limit configurations
 */
export const rateLimitConfigs = {
  // General API rate limiting
  general: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // 100 requests per 15 minutes
    message: 'เรียกใช้ API มากเกินไป กรุณารอสักครู่แล้วลองใหม่'
  },
  
  // Login rate limiting (stricter)
  login: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 login attempts per 15 minutes
    message: 'พยายามเข้าสู่ระบบมากเกินไป กรุณารอ 15 นาทีแล้วลองใหม่'
  },
  
  // Registration rate limiting
  register: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3, // 3 registrations per hour
    message: 'สมัครสมาชิกมากเกินไป กรุณารอ 1 ชั่วโมงแล้วลองใหม่'
  },
  
  // Password reset rate limiting
  passwordReset: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3, // 3 password reset attempts per hour
    message: 'ขอรีเซ็ตรหัสผ่านมากเกินไป กรุณารอ 1 ชั่วโมงแล้วลองใหม่'
  },
  
  // Refresh token rate limiting
  refreshToken: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 10, // 10 refresh attempts per 5 minutes
    message: 'ต่ออายุ token มากเกินไป กรุณารอสักครู่แล้วลองใหม่'
  }
};

/**
 * Create rate limiter for specific endpoint
 */
export function createRateLimiter(configName: keyof typeof rateLimitConfigs) {
  const config = rateLimitConfigs[configName];
  const limiter = rateLimit(config);
  
  return (req: NextRequest, identifier?: string) => {
    const clientId = identifier || getClientIdentifier(req);
    const result = limiter(clientId);
    
    console.log(`🚦 Rate limit check for ${configName}:`, {
      identifier: clientId,
      success: result.success,
      remaining: result.remaining,
      limit: result.limit
    });
    
    return result;
  };
}

/**
 * Rate limit response headers
 */
export function addRateLimitHeaders(response: Response, result: RateLimitResult): Response {
  response.headers.set('X-RateLimit-Limit', result.limit.toString());
  response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
  response.headers.set('X-RateLimit-Reset', new Date(result.resetTime).toISOString());
  
  return response;
}

/**
 * Create rate limit error response
 */
export function createRateLimitResponse(result: RateLimitResult) {
  const response = new Response(JSON.stringify({
    error: result.message,
    retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000)
  }), {
    status: 429,
    headers: {
      'Content-Type': 'application/json',
      'Retry-After': Math.ceil((result.resetTime - Date.now()) / 1000).toString(),
      'X-RateLimit-Limit': result.limit.toString(),
      'X-RateLimit-Remaining': result.remaining.toString(),
      'X-RateLimit-Reset': new Date(result.resetTime).toISOString()
    }
  });

  return response;
}

/**
 * Reset rate limit for successful operations (ไม่ count เมื่อสำเร็จ)
 */
export function resetRateLimit(identifier: string): void {
  if (store[identifier]) {
    // Reset count to 0 for successful operations
    store[identifier].count = Math.max(0, store[identifier].count - 1);
    console.log(`✅ Rate limit reset for successful operation:`, identifier);
  }
}

/**
 * Get rate limit status without incrementing
 */
export function getRateLimitStatus(configName: keyof typeof rateLimitConfigs, identifier: string): RateLimitResult {
  const config = rateLimitConfigs[configName];
  const now = Date.now();

  if (!store[identifier] || store[identifier].resetTime < now) {
    return {
      success: true,
      limit: config.maxRequests,
      remaining: config.maxRequests,
      resetTime: now + config.windowMs
    };
  }

  const entry = store[identifier];
  return {
    success: entry.count < config.maxRequests,
    limit: config.maxRequests,
    remaining: Math.max(0, config.maxRequests - entry.count),
    resetTime: entry.resetTime
  };
}
