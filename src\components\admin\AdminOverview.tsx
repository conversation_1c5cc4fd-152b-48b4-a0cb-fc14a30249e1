// AdminOverview.tsx
"use client";

import { 
  Users, 
  UserCheck, 
  ClipboardCheck, 
  Utensils, 
  Activity, 
  Target,
  TrendingUp,
  Award,
  CheckCircle,
  XCircle,
  Clock,
  Database,
  FileText
} from "lucide-react";

export default function AdminOverview() {
  const dashboardStats = {
    totalUsers: 1247,
    activeUsers: 892,
    pendingVerifications: 45,
    totalMeals: 15632,
    averageCaloriesPerUser: 1850,
    completionRate: 78.5
  };

  const recentUsers = [
    { id: 1, name: 'สมชาย ใจดี', status: 'active', lastLogin: '2024-06-22 09:30' },
    { id: 2, name: 'สุดา วงศ์วิทย์', status: 'pending', lastLogin: '2024-06-21 14:20' },
    { id: 3, name: 'ประยุทธ์ สีเขียว', status: 'active', lastLogin: '2024-06-22 11:15' },
    { id: 4, name: 'มาลี แสงทอง', status: 'inactive', lastLogin: '2024-06-19 16:45' }
  ];

  const pendingVerifications = [
    { id: 1, user: 'สมชาย ใจดี', meal: 'มื้อเช้า', time: '07:30' },
    { id: 2, user: 'สุดา วงศ์วิทย์', meal: 'มื้อกลางวัน', time: '12:15' },
    { id: 3, user: 'ประยุทธ์ สีเขียว', meal: 'มื้อเย็น', time: '18:30' }
  ];

  // Define the allowed color keys for StatCard
  const colorMap = {
    emerald: {
      from: 'from-emerald-400',
      to: 'to-emerald-600',
    },
    blue: {
      from: 'from-blue-400',
      to: 'to-blue-600',
    },
    amber: {
      from: 'from-amber-400',
      to: 'to-amber-600',
    },
    purple: {
      from: 'from-purple-400',
      to: 'to-purple-600',
    },
    red: {
      from: 'from-red-400',
      to: 'to-red-600',
    },
    green: {
      from: 'from-green-400',
      to: 'to-green-600',
    },
    orange: {
      from: 'from-orange-400',
      to: 'to-orange-600',
    },
  } as const;

  type StatCardColor = keyof typeof colorMap;

  type StatCardProps = {
    title: string;
    value: string | number;
    change?: number;
    icon: React.ComponentType<{ className?: string }>;
    color?: StatCardColor;
  };

  const StatCard = ({ title, value, change, icon: Icon, color = "emerald" }: StatCardProps) => {
    const gradientFrom = colorMap[color]?.from || 'from-emerald-400';
    const gradientTo = colorMap[color]?.to || 'to-emerald-600';
    return (
      <div className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 border border-white/50 shadow-lg hover:shadow-xl transition-all duration-300">
        <div className="flex items-center justify-between mb-4">
          <div className={`w-12 h-12 bg-gradient-to-br ${gradientFrom} ${gradientTo} rounded-xl flex items-center justify-center shadow-lg`}>
            <Icon className="w-6 h-6 text-white" />
          </div>
          {change !== undefined && change !== null && (
            <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
              change > 0 ? 'bg-emerald-100 text-emerald-700' : 'bg-red-100 text-red-700'
            }`}>
              <TrendingUp className={`w-3 h-3 ${change < 0 ? 'rotate-180' : ''}`} />
              {Math.abs(change)}%
            </div>
          )}
        </div>
        <h3 className="text-2xl font-bold text-gray-800 mb-1">{value}</h3>
        <p className="text-sm text-gray-600">{title}</p>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <StatCard 
          title="ผู้ใช้ทั้งหมด" 
          value={dashboardStats.totalUsers.toLocaleString()} 
          change={12} 
          icon={Users} 
          color="emerald" 
        />
        <StatCard 
          title="ผู้ใช้ที่ใช้งาน" 
          value={dashboardStats.activeUsers.toLocaleString()} 
          change={8} 
          icon={UserCheck} 
          color="blue" 
        />
        <StatCard 
          title="รอตรวจสอบ" 
          value={dashboardStats.pendingVerifications} 
          change={-5} 
          icon={ClipboardCheck} 
          color="amber" 
        />
        <StatCard 
          title="มื้อทั้งหมด" 
          value={dashboardStats.totalMeals.toLocaleString()} 
          change={15} 
          icon={Utensils} 
          color="purple" 
        />
        <StatCard 
          title="แคลอรี่เฉลี่ย/วัน" 
          value={dashboardStats.averageCaloriesPerUser} 
          change={3} 
          icon={Activity} 
          color="red" 
        />
        <StatCard 
          title="อัตราความสำเร็จ" 
          value={`${dashboardStats.completionRate}%`} 
          change={2} 
          icon={Target} 
          color="green" 
        />
      </div>

      {/* Quick Actions */}
      <div className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 border border-white/50 shadow-lg">
        <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
          <Award className="w-5 h-5 text-emerald-600" />
          การดำเนินการด่วน
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="flex items-center gap-3 p-4 bg-emerald-50 hover:bg-emerald-100 rounded-xl transition-colors group">
            <ClipboardCheck className="w-5 h-5 text-emerald-600" />
            <div className="text-left">
              <div className="font-medium text-emerald-700">ตรวจสอบข้อมูล</div>
              <div className="text-sm text-emerald-600">{dashboardStats.pendingVerifications} รายการ</div>
            </div>
          </button>
          <button className="flex items-center gap-3 p-4 bg-blue-50 hover:bg-blue-100 rounded-xl transition-colors group">
            <Users className="w-5 h-5 text-blue-600" />
            <div className="text-left">
              <div className="font-medium text-blue-700">จัดการผู้ใช้</div>
              <div className="text-sm text-blue-600">ดูรายชื่อทั้งหมด</div>
            </div>
          </button>
          <button className="flex items-center gap-3 p-4 bg-purple-50 hover:bg-purple-100 rounded-xl transition-colors group">
            <Database className="w-5 h-5 text-purple-600" />
            <div className="text-left">
              <div className="font-medium text-purple-700">ฐานข้อมูลอาหาร</div>
              <div className="text-sm text-purple-600">จัดการรายการอาหาร</div>
            </div>
          </button>
          <button className="flex items-center gap-3 p-4 bg-orange-50 hover:bg-orange-100 rounded-xl transition-colors group">
            <FileText className="w-5 h-5 text-orange-600" />
            <div className="text-left">
              <div className="font-medium text-orange-700">สร้างรายงาน</div>
              <div className="text-sm text-orange-600">ออกรายงานแบบละเอียด</div>
            </div>
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 border border-white/50 shadow-lg">
          <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
            <Users className="w-5 h-5 text-emerald-600" />
            ผู้ใช้ล่าสุด
          </h3>
          <div className="space-y-3">
            {recentUsers.map(user => (
              <div key={user.id} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-teal-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-medium text-xs">{user.name.charAt(0)}</span>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 text-sm">{user.name}</div>
                    <div className="text-xs text-gray-500">{user.lastLogin}</div>
                  </div>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  user.status === 'active' ? 'bg-emerald-100 text-emerald-700' :
                  user.status === 'pending' ? 'bg-yellow-100 text-yellow-700' :
                  'bg-gray-100 text-gray-700'
                }`}>
                  {user.status === 'active' ? 'ใช้งาน' : 
                   user.status === 'pending' ? 'รอยืนยัน' : 'ไม่ใช้งาน'}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 border border-white/50 shadow-lg">
          <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center gap-2">
            <ClipboardCheck className="w-5 h-5 text-amber-600" />
            รอการตรวจสอบ
          </h3>
          <div className="space-y-3">
            {pendingVerifications.map(item => (
              <div key={item.id} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center">
                    <Clock className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 text-sm">{item.user}</div>
                    <div className="text-xs text-gray-500">{item.meal} - {item.time}</div>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <button className="p-1 hover:bg-emerald-100 rounded transition-colors" title="อนุมัติ">
                    <CheckCircle className="w-4 h-4 text-emerald-600" />
                  </button>
                  <button className="p-1 hover:bg-red-100 rounded transition-colors" title="ปฏิเสธ">
                    <XCircle className="w-4 h-4 text-red-600" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}