import React, { useState, useEffect, useRef } from "react";
import { 
  Bell, 
  Settings, 
  User, 
  Utensils, 
  ChevronDown, 
  Menu, 
  X,
  PlusCircle,
  BarChart3,
  ClipboardList,
  Calendar,
  Target,
  Book
} from "lucide-react";
import AddMealModal from "../form/AddMealModal";

interface HeaderProps {
  userName?: string;
  isLoadingUser?: boolean;
  isLoggedIn?: boolean;
  onLogoutClick?: () => void;
  currentPage?: string;
  onAddMealClick?: () => void;
}

export default function Header({ 
  userName = "ผู้ใช้งาน", 
  isLoadingUser = false, 
  isLoggedIn = true, 
  onLogoutClick = () => {},
  currentPage = "dashboard",
  onAddMealClick = () => {}
}: HeaderProps) {
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const [showAddModal, setShowAddModal] = useState(false);
  const profileRef = useRef<HTMLDivElement>(null);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (profileRef.current && !profileRef.current.contains(event.target as Node)) {
        setIsProfileOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const navbarClass = `
    fixed top-0 left-0 right-0 z-50 transition-all duration-300 ease-out
    ${scrollY > 20 
      ? 'bg-white/95 backdrop-blur-xl border-b border-gray-200/60 shadow-lg shadow-gray-500/10' 
      : 'bg-white/80 backdrop-blur-lg border-b border-gray-200/30'
    }
  `;

  // Navigation items for users
  const navigationItems = [
    {
      name: "หน้าหลัก",
      href: "/",
      icon: Utensils,
      active: currentPage === "dashboard",
      description: "กลับหน้าหลัก",
      isButton: false
    },
    {
      name: "บันทึกอาหาร",
      action: () => setShowAddModal(true),
      icon: PlusCircle,
      active: currentPage === "record-food",
      description: "บันทึกอาหารมื้อใหม่",
      isButton: true
    },
    {
      name: "รายงาน",
      href: "/reports",
      icon: BarChart3,
      active: currentPage === "reports",
      description: "ดูรายงานโภชนาการ",
      isButton: false
    },
    {
      name: "แบบสอบถาม",
      href: "/ffq",
      icon: ClipboardList,
      active: currentPage === "ffq",
      description: "FFQ และแบบสอบถาม",
      isButton: false
    },
    {
      name: "ประวัติ",
      href: "/history",
      icon: Calendar,
      active: currentPage === "history",
      description: "ประวัติการบันทึก",
      isButton: false
    }
  ];

  return (
    <>
      {/* Spacer to prevent content overlap */}
      <div className="h-16"></div>
      
      <header className={navbarClass}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <div className="flex items-center justify-between h-16">
            {/* Logo Section */}
            <div className="flex items-center space-x-4">
              <div className="relative group">
                <div className="absolute -inset-1 bg-gradient-to-r from-emerald-400 to-teal-600 rounded-xl blur opacity-75 group-hover:opacity-100 transition duration-300"></div>
                <div className="relative w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Utensils className="w-5 h-5 text-white" />
                </div>
              </div>
              <div className="hidden lg:block">
                <h1 className="text-xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  ระบบบันทึกโภชนาการ
                </h1>
                <div className="text-xs text-gray-600 flex items-center gap-1">
                  สวัสดี{" "}
                  {isLoadingUser ? (
                    <div className="w-16 h-3 bg-gray-200 rounded-full animate-pulse"></div>
                  ) : (
                    <span className="font-medium text-emerald-700 animate-fade-in">
                      {userName}
                    </span>
                  )}
                  <span className="animate-pulse">👋</span>
                </div>
              </div>
              {/* Mobile/Tablet Title */}
              <span className="block lg:hidden text-base font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                บันทึกโภชนาการ
              </span>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-1">
              {navigationItems.map((item) => (
                item.isButton ? (
                  <button
                    key={item.name}
                    onClick={item.action}
                    className={`
                      flex items-center space-x-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 group
                      ${item.active 
                        ? 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg shadow-emerald-500/25' 
                        : 'text-gray-700 hover:bg-gray-100/80 hover:text-emerald-600'
                      }
                    `}
                    title={item.description}
                  >
                    <item.icon className={`w-4 h-4 ${item.active ? 'text-white' : 'group-hover:text-emerald-600'}`} />
                    <span>{item.name}</span>
                  </button>
                ) : (
                  <a
                    key={item.name}
                    href={item.href}
                    className={`
                      flex items-center space-x-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 group
                      ${item.active 
                        ? 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg shadow-emerald-500/25' 
                        : 'text-gray-700 hover:bg-gray-100/80 hover:text-emerald-600'
                      }
                    `}
                    title={item.description}
                  >
                    <item.icon className={`w-4 h-4 ${item.active ? 'text-white' : 'group-hover:text-emerald-600'}`} />
                    <span>{item.name}</span>
                  </a>
                )
              ))}
            </div>

            {/* Desktop Right Section */}
            <div className="hidden lg:flex items-center space-x-3">
              {/* Notifications */}
              <div className="relative group">
                <button className="p-2.5 rounded-xl bg-gray-50/80 hover:bg-gray-100/80 transition-all duration-200 group-hover:scale-105 border border-gray-200/50">
                  <Bell className="w-5 h-5 text-gray-600" />
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-red-400 to-red-500 rounded-full border-2 border-white shadow-sm animate-pulse"></span>
                </button>
              </div>

              {/* User Profile */}
              {isLoggedIn ? (
                <div className="relative" ref={profileRef}>
                  <button
                    onClick={() => setIsProfileOpen(!isProfileOpen)}
                    className="flex items-center space-x-3 px-4 py-2 rounded-xl bg-gray-50/80 hover:bg-gray-100/80 transition-all duration-200 hover:scale-105 border border-gray-200/50 group"
                  >
                    <div className="w-8 h-8 bg-gradient-to-br from-emerald-400 to-teal-600 rounded-lg flex items-center justify-center shadow-sm">
                      <User className="w-4 h-4 text-white" />
                    </div>
                    <span className="text-sm font-medium text-gray-700 max-w-24 truncate">
                      {userName}
                    </span>
                    <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${isProfileOpen ? 'rotate-180' : ''}`} />
                  </button>

                  {/* Dropdown Menu */}
                  <div className={`
                    absolute right-0 mt-2 w-72 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 
                    transition-all duration-200 origin-top-right z-50
                    ${isProfileOpen 
                      ? 'opacity-100 scale-100 translate-y-0' 
                      : 'opacity-0 scale-95 -translate-y-2 pointer-events-none'
                    }
                  `}>
                    <div className="p-4">
                      {/* User Info */}
                      <div className="flex items-center space-x-3 pb-4 border-b border-gray-200/50">
                        <div className="w-12 h-12 bg-gradient-to-br from-emerald-400 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                          <User className="w-6 h-6 text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-semibold text-gray-900 truncate">
                            {userName}
                          </p>
                          <p className="text-xs text-gray-600">
                            ผู้ใช้งาน
                          </p>
                        </div>
                      </div>

                      {/* Menu Items */}
                      <div className="py-2 space-y-1">
                        <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100/80 rounded-lg transition-colors flex items-center space-x-3">
                          <User className="w-4 h-4" />
                          <span>ข้อมูลส่วนตัว</span>
                        </button>
                        <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100/80 rounded-lg transition-colors flex items-center space-x-3">
                          <Target className="w-4 h-4" />
                          <span>เป้าหมายโภชนาการ</span>
                        </button>
                        <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100/80 rounded-lg transition-colors flex items-center space-x-3">
                          <Book className="w-4 h-4" />
                          <span>คู่มือการใช้งาน</span>
                        </button>
                        <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100/80 rounded-lg transition-colors flex items-center space-x-3">
                          <Settings className="w-4 h-4" />
                          <span>การตั้งค่า</span>
                        </button>
                      </div>

                      {/* Logout */}
                      <div className="pt-2 border-t border-gray-200/50">
                        <button
                          onClick={() => {
                            onLogoutClick();
                            setIsProfileOpen(false);
                          }}
                          className="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50/80 rounded-lg transition-colors"
                        >
                          ออกจากระบบ
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <a
                    href="/login"
                    className="px-4 py-2 text-sm font-medium text-emerald-600 hover:text-emerald-700 hover:bg-emerald-50/80 rounded-xl transition-all duration-200 border border-emerald-200/50"
                  >
                    เข้าสู่ระบบ
                  </a>
                  <a
                    href="/register"
                    className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 rounded-xl transition-all duration-200 shadow-lg shadow-emerald-500/25 hover:shadow-emerald-500/40 hover:scale-105"
                  >
                    สมัครสมาชิก
                  </a>
                </div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <div className="lg:hidden">
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="p-2 rounded-xl bg-gray-50/80 hover:bg-gray-100/80 transition-all duration-200 border border-gray-200/50"
              >
                {isMobileMenuOpen ? (
                  <X className="w-5 h-5 text-gray-600" />
                ) : (
                  <Menu className="w-5 h-5 text-gray-600" />
                )}
              </button>
            </div>
          </div>

          {/* Mobile Menu */}
          <div className={`
            lg:hidden overflow-hidden transition-all duration-300 ease-in-out
            ${isMobileMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'}
          `}>
            <div className="py-4 space-y-3 border-t border-gray-200/50">
              {/* Mobile User Info */}
              <div className="flex items-center space-x-3 px-4 py-3 bg-gray-50/80 rounded-xl">
                <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 to-teal-600 rounded-xl flex items-center justify-center">
                  <User className="w-5 h-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-semibold text-gray-900">
                    สวัสดี {userName}
                  </p>
                  <p className="text-xs text-gray-600">ผู้ใช้งาน</p>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="grid grid-cols-1 gap-2">
                <button 
                  onClick={() => setShowAddModal(true)}
                  className="flex items-center justify-center space-x-2 px-4 py-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white rounded-xl text-sm font-medium"
                >
                  <PlusCircle className="w-4 h-4" />
                  <span>บันทึกอาหาร</span>
                </button>
              </div>

              {/* Mobile Navigation Items */}
              <div className="space-y-2">
                {navigationItems.map((item) => (
                  item.isButton ? (
                    <button
                      key={item.name}
                      onClick={item.action}
                      className={`
                        flex items-center space-x-3 px-4 py-3 text-sm rounded-xl transition-colors w-full
                        ${item.active 
                          ? 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white' 
                          : 'text-gray-700 hover:bg-gray-100/80'
                        }
                      `}
                    >
                      <item.icon className="w-5 h-5" />
                      <div>
                        <div className="font-medium">{item.name}</div>
                        <div className={`text-xs ${item.active ? 'text-emerald-100' : 'text-gray-500'}`}>
                          {item.description}
                        </div>
                      </div>
                    </button>
                  ) : (
                    <a
                      key={item.name}
                      href={item.href}
                      className={`
                        flex items-center space-x-3 px-4 py-3 text-sm rounded-xl transition-colors
                        ${item.active 
                          ? 'bg-gradient-to-r from-emerald-500 to-teal-600 text-white' 
                          : 'text-gray-700 hover:bg-gray-100/80'
                        }
                      `}
                    >
                      <item.icon className="w-5 h-5" />
                      <div>
                        <div className="font-medium">{item.name}</div>
                        <div className={`text-xs ${item.active ? 'text-emerald-100' : 'text-gray-500'}`}>
                          {item.description}
                        </div>
                      </div>
                    </a>
                  )
                ))}
              </div>

              {/* Mobile Additional Menu Items */}
              <div className="space-y-2 pt-2 border-t border-gray-200/50">
                <button className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-gray-100/80 rounded-xl transition-colors">
                  <Bell className="w-5 h-5" />
                  <span>การแจ้งเตือน</span>
                  <span className="ml-auto w-2 h-2 bg-red-400 rounded-full"></span>
                </button>
                <button className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-gray-100/80 rounded-xl transition-colors">
                  <Target className="w-5 h-5" />
                  <span>เป้าหมายโภชนาการ</span>
                </button>
                <button className="w-full flex items-center space-x-3 px-4 py-3 text-sm text-gray-700 hover:bg-gray-100/80 rounded-xl transition-colors">
                  <Settings className="w-5 h-5" />
                  <span>การตั้งค่า</span>
                </button>
                
                {isLoggedIn ? (
                  <button
                    onClick={() => {
                      onLogoutClick();
                      setIsMobileMenuOpen(false);
                    }}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-3 text-sm text-red-600 hover:bg-red-50/80 rounded-xl transition-colors border border-red-300 bg-white font-semibold"
                  >
                    <span>ออกจากระบบ</span>
                  </button>
                ) : (
                  <div className="space-y-2 pt-2">
                    <a
                      href="/login"
                      className="block w-full text-center px-4 py-3 text-sm font-medium text-emerald-600 hover:bg-emerald-50/80 rounded-xl transition-colors border border-emerald-200/50"
                    >
                      เข้าสู่ระบบ
                    </a>
                    <a
                      href="/register"
                      className="block w-full text-center px-4 py-3 text-sm font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl shadow-lg"
                    >
                      สมัครสมาชิก
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Add Meal Modal */}
      <AddMealModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSubmit={() => setShowAddModal(false)}
      />

      <style jsx>{`
        @keyframes fade-in {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in {
          animation: fade-in 0.5s ease-out forwards;
        }
        
        /* Global body padding to prevent navbar overlap */
        body {
          padding-top: 0 !important;
        }
      `}</style>
    </>
  );
}