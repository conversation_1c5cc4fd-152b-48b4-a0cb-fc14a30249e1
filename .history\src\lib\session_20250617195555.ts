// src/lib/session.ts
import { getIronSession } from 'iron-session';
import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

export interface SessionUser {
  id: number;
  email: string;
  fullName: string;
  gender: string;
  height: number;
  weight: number;
  activityLevel: string;
  goal: string;
  foodAllergies?: string;
  bmr?: number;
  tdee?: number;
  role: string;
  bmi: number;
  age: number;
  loginTime: number;
  lastActivity: number;
}

export interface SavedCredential {
  email: string;
  password: string; // Encrypted password for autofill
  lastUsed: number;
  displayName?: string;
}

export interface SessionData {
  user?: SessionUser;
  isLoggedIn: boolean;
  loginHistory: Array<{
    email: string;
    loginTime: number;
    userAgent?: string;
  }>;
  savedCredentials: SavedCredential[];
}

// Session configuration
export const sessionOptions = {
  password: process.env.SESSION_SECRET || 'complex_password_at_least_32_characters_long_for_session_autofill_system_security',
  cookieName: 'nutrition-session',
  cookieOptions: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    // ไม่ตั้ง maxAge = session จะหมดอายุเมื่อปิด browser
    // maxAge: undefined, // Session cookie (หมดอายุเมื่อปิด browser)
    sameSite: 'strict' as const,
  },
};

// Simple encryption for autofill passwords
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'autofill-key-32-characters-long-for-session-based-auth';

function encryptPassword(password: string): string {
  try {
    // Use a simple base64 encoding for autofill (not for production security)
    return Buffer.from(password, 'utf8').toString('base64');
  } catch (error) {
    console.error('Password encryption failed:', error);
    return password; // Return original if encryption fails
  }
}

function decryptPassword(encryptedPassword: string): string {
  try {
    // Decode from base64
    return Buffer.from(encryptedPassword, 'base64').toString('utf8');
  } catch (error) {
    console.error('Password decryption failed:', error);
    return '';
  }
}

/**
 * Get session from request
 */
export async function getSession(req: NextRequest): Promise<SessionData> {
  try {
    const session = await getIronSession<SessionData>(req, NextResponse.next(), sessionOptions);
    
    // Initialize session if empty
    if (!session.isLoggedIn) {
      session.isLoggedIn = false;
      session.loginHistory = session.loginHistory || [];
      session.savedCredentials = session.savedCredentials || [];
    }
    
    return session;
  } catch (error) {
    console.error('Error getting session:', error);
    return {
      isLoggedIn: false,
      loginHistory: [],
      savedCredentials: []
    };
  }
}

/**
 * Create session for user
 */
export async function createSession(
  req: NextRequest, 
  res: NextResponse, 
  user: SessionUser,
  credentials: { email: string; password: string; rememberMe: boolean },
  userAgent?: string
): Promise<void> {
  try {
    const session = await getIronSession<SessionData>(req, res, sessionOptions);
    
    // Set user data
    session.user = user;
    session.isLoggedIn = true;
    
    // Initialize arrays if not exists
    if (!session.loginHistory) {
      session.loginHistory = [];
    }
    if (!session.savedCredentials) {
      session.savedCredentials = [];
    }
    
    // Add to login history (keep last 5 logins เพื่อ<|im_start|>ความเร็ว)
    const loginEntry = {
      email: user.email,
      loginTime: Date.now(),
      userAgent: userAgent || 'Unknown'
    };

    session.loginHistory.unshift(loginEntry);
    session.loginHistory = session.loginHistory.slice(0, 5);
    
    // Save credentials for autofill if remember me is enabled
    if (credentials.rememberMe) {
      await saveCredentialsForAutofill(session, credentials.email, credentials.password, user.fullName);
    }
    
    await session.save();
    
    console.log('✅ Session created for user:', user.email);
  } catch (error) {
    console.error('❌ Error creating session:', error);
    throw error;
  }
}

/**
 * Save credentials for autofill
 */
async function saveCredentialsForAutofill(
  session: SessionData, 
  email: string, 
  password: string, 
  displayName?: string
): Promise<void> {
  // Remove existing credential for this email
  session.savedCredentials = session.savedCredentials.filter(cred => cred.email !== email);
  
  // Add new credential
  const encryptedPassword = encryptPassword(password);
  const newCredential: SavedCredential = {
    email,
    password: encryptedPassword,
    lastUsed: Date.now(),
    displayName
  };
  
  session.savedCredentials.unshift(newCredential);

  // Keep only last 3 saved credentials เพื่อ<|im_start|>ความเร็ว
  session.savedCredentials = session.savedCredentials.slice(0, 3);
  
  console.log('✅ Credentials saved for autofill:', email);
}

/**
 * Get most recent saved credential for autofill
 */
export async function getMostRecentCredential(req: NextRequest): Promise<{
  email: string;
  password: string;
  displayName?: string;
} | null> {
  try {
    const session = await getSession(req);
    
    if (!session.savedCredentials || session.savedCredentials.length === 0) {
      return null;
    }
    
    // Sort by last used (most recent first)
    const sortedCredentials = session.savedCredentials.sort((a, b) => b.lastUsed - a.lastUsed);
    const mostRecent = sortedCredentials[0];
    
    return {
      email: mostRecent.email,
      password: decryptPassword(mostRecent.password),
      displayName: mostRecent.displayName
    };
  } catch (error) {
    console.error('Error getting most recent credential:', error);
    return null;
  }
}

/**
 * Get saved credentials for autofill (without passwords for security)
 */
export async function getSavedCredentials(req: NextRequest): Promise<Array<{
  email: string;
  displayName?: string;
  savedAt: string;
}>> {
  try {
    const session = await getSession(req);

    if (!session.savedCredentials || session.savedCredentials.length === 0) {
      return [];
    }

    // Sort by last used (most recent first) and return without passwords
    const sortedCredentials = session.savedCredentials.sort((a, b) => b.lastUsed - a.lastUsed);

    return sortedCredentials.map(cred => ({
      email: cred.email,
      displayName: cred.displayName,
      savedAt: new Date(cred.lastUsed).toLocaleString('th-TH')
    }));
  } catch (error) {
    console.error('Error getting saved credentials:', error);
    return [];
  }
}

/**
 * Update session activity
 */
export async function updateSessionActivity(req: NextRequest, res: NextResponse): Promise<void> {
  try {
    const session = await getIronSession<SessionData>(req, res, sessionOptions);
    
    if (session.user) {
      session.user.lastActivity = Date.now();
      await session.save();
    }
  } catch (error) {
    console.error('❌ Error updating session activity:', error);
  }
}

/**
 * Destroy session
 */
export async function destroySession(req: NextRequest, res: NextResponse): Promise<void> {
  try {
    const session = await getIronSession<SessionData>(req, res, sessionOptions);
    
    // Keep login history and saved credentials but clear user data
    const loginHistory = session.loginHistory || [];
    const savedCredentials = session.savedCredentials || [];
    
    session.user = undefined;
    session.isLoggedIn = false;
    session.loginHistory = loginHistory;
    session.savedCredentials = savedCredentials;
    
    await session.save();
    
    console.log('✅ Session destroyed');
  } catch (error) {
    console.error('❌ Error destroying session:', error);
    throw error;
  }
}

/**
 * Check if session is valid (simplified for browser session)
 */
export function isSessionValid(session: SessionData): boolean {
  if (!session.isLoggedIn || !session.user) {
    return false;
  }

  // เช็คแค่ว่า session มีข้อมูล user หรือไม่
  // ไม่เช็ค expiry เพราะเป็น browser session
  return true;
}
