// AdminVerifications.tsx
"use client";

import { Camera, CheckCircle, XCircle } from "lucide-react";

export default function AdminVerifications() {
  const pendingVerifications = [
    { 
      id: 1, 
      user: 'สมชาย ใจดี', 
      meal: 'มื้อเช้า', 
      time: '07:30', 
      date: '2024-06-22', 
      foods: ['ข้าวต้มกุ้ง', 'ไข่ดาว', 'กาแฟดำ'],
      calories: 450,
      hasBeforeImage: true,
      hasAfterImage: false
    },
    { 
      id: 2, 
      user: 'สุดา วงศ์วิทย์', 
      meal: 'มื้อกลางวัน', 
      time: '12:15', 
      date: '2024-06-22', 
      foods: ['ข้าวผัดกุ้ง', 'ต้มยำกุ้ง', 'น้ำเปล่า'],
      calories: 680,
      hasBeforeImage: true,
      hasAfterImage: true
    },
    { 
      id: 3, 
      user: 'ประยุทธ์ สีเขียว', 
      meal: 'มื้อเย็น', 
      time: '18:30', 
      date: '2024-06-21', 
      foods: ['ข้าวกับแกงเขียวหวานไก่', 'ผัดผักบุ้ง'],
      calories: 520,
      hasBeforeImage: true,
      hasAfterImage: false
    },
    { 
      id: 4, 
      user: 'มาลี แสงทอง', 
      meal: 'ของว่าง', 
      time: '15:45', 
      date: '2024-06-22', 
      foods: ['ผลไม้รวม', 'น้ำส้มคั้น'],
      calories: 180,
      hasBeforeImage: false,
      hasAfterImage: false
    }
  ];

  return (
    <div className="space-y-6">
      <div className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 border border-white/50 shadow-lg">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
          <h3 className="text-lg font-bold text-gray-800">ตรวจสอบและยืนยันข้อมูล</h3>
          <div className="flex items-center gap-3">
            <select className="px-4 py-2 border border-gray-200 rounded-xl focus:border-emerald-400 focus:ring-2 focus:ring-emerald-100">
              <option>ทั้งหมด</option>
              <option>รอตรวจสอบ</option>
              <option>ยืนยันแล้ว</option>
              <option>ปฏิเสธ</option>
            </select>
            <input
              type="date"
              className="px-4 py-2 border border-gray-200 rounded-xl focus:border-emerald-400 focus:ring-2 focus:ring-emerald-100"
              defaultValue="2024-06-22"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {pendingVerifications.map(item => (
            <div key={item.id} className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-medium text-sm">{item.user.charAt(0)}</span>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{item.user}</div>
                    <div className="text-sm text-gray-500">{item.date} • {item.time}</div>
                  </div>
                </div>
                <span className="px-3 py-1 bg-amber-100 text-amber-700 rounded-full text-sm font-medium">
                  {item.meal}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                {/* Before Image */}
                <div>
                  <div className="text-xs text-gray-500 mb-2 flex items-center gap-1">
                    <Camera className="w-3 h-3" /> รูปก่อนทาน
                  </div>
                  {item.hasBeforeImage ? (
                    <div className="relative group cursor-pointer">
                      <div className="w-full h-24 bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-lg overflow-hidden">
                        <div className="w-full h-full bg-emerald-300 flex items-center justify-center">
                          <span className="text-emerald-700 text-xs">มีรูปภาพ</span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="w-full h-24 bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                      <div className="text-center">
                        <Camera className="w-6 h-6 text-gray-400 mx-auto mb-1" />
                        <div className="text-xs text-gray-500">ไม่มีรูป</div>
                      </div>
                    </div>
                  )}
                </div>
                
                {/* After Image */}
                <div>
                  <div className="text-xs text-gray-500 mb-2 flex items-center gap-1">
                    <Camera className="w-3 h-3" /> รูปหลังทาน
                  </div>
                  {item.hasAfterImage ? (
                    <div className="relative group cursor-pointer">
                      <div className="w-full h-24 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg overflow-hidden">
                        <div className="w-full h-full bg-blue-300 flex items-center justify-center">
                          <span className="text-blue-700 text-xs">มีรูปภาพ</span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="w-full h-24 bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                      <div className="text-center">
                        <Camera className="w-6 h-6 text-gray-400 mx-auto mb-1" />
                        <div className="text-xs text-gray-500">ไม่มีรูป</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Food Items */}
              <div className="mb-4">
                <div className="text-xs text-gray-500 mb-2">รายการอาหาร</div>
                <div className="flex flex-wrap gap-2">
                  {item.foods.map((food, index) => (
                    <span key={index} className="px-2 py-1 bg-emerald-50 text-emerald-700 rounded-full text-xs border border-emerald-200">
                      {food}
                    </span>
                  ))}
                </div>
              </div>

              {/* Calories and Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">แคลอรี่:</span>
                  <span className="px-2 py-1 bg-amber-100 text-amber-700 rounded-lg text-sm font-medium">
                    {item.calories} kcal
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <button className="flex items-center gap-1 px-3 py-2 bg-emerald-100 text-emerald-700 rounded-lg text-sm font-medium hover:bg-emerald-200 transition-colors">
                    <CheckCircle className="w-4 h-4" />
                    อนุมัติ
                  </button>
                  <button className="flex items-center gap-1 px-3 py-2 bg-red-100 text-red-700 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors">
                    <XCircle className="w-4 h-4" />
                    ปฏิเสธ
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Summary */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="bg-amber-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-amber-700">{pendingVerifications.length}</div>
              <div className="text-sm text-amber-600">รอตรวจสอบ</div>
            </div>
            <div className="bg-emerald-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-emerald-700">28</div>
              <div className="text-sm text-emerald-600">อนุมัติแล้ววันนี้</div>
            </div>
            <div className="bg-red-50 rounded-lg p-4">
              <div className="text-2xl font-bold text-red-700">3</div>
              <div className="text-sm text-red-600">ปฏิเสธวันนี้</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}