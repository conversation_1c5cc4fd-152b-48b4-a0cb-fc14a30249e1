'use client';

import { useState, useEffect } from 'react';
import { 
  ClipboardList, 
  Users, 
  Calendar,
  CheckCircle,
  Clock,
  ArrowRight,
  FileText,
  Target,
  TrendingUp
} from 'lucide-react';

import Link from 'next/link';
import Header from '@/components/home/<USER>';

interface FFQSet {
  id: number;
  name: string;
  description: string;
  isActive: boolean;
  questionsCount?: number;
  estimatedTime?: number;
  category?: string;
}

export default function FFQListPage() {
  // User authentication states - เหมือนหน้าหลัก
  const [userData, setUserData] = useState({
    name: "ผู้ใช้งาน",
    age: 25,
    weight: 70,
    height: 175,
    bmi: 22.9,
    goal: "health",
    dailyCalories: 2200,
    consumedCalories: 0,
    remainingCalories: 2200
  });

  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [sessionInfo, setSessionInfo] = useState(null);
  const [loginHistory, setLoginHistory] = useState([]);
  const [isLoadingUser, setIsLoadingUser] = useState(true);

  // FFQ specific states
  const [sets, setSets] = useState<FFQSet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize session-based authentication - เหมือนหน้าหลัก
  useEffect(() => {
    const checkSession = async () => {
      // ตรวจสอบ preloaded data จาก login ก่อน
      const tempUserData = sessionStorage.getItem('tempUserData');
      if (tempUserData) {
        try {
          const preloadedData = JSON.parse(tempUserData);
          setUserData(prev => ({
            ...prev,
            name: preloadedData.name,
            age: preloadedData.age,
            weight: preloadedData.weight,
            height: preloadedData.height,
            bmi: preloadedData.bmi,
            goal: preloadedData.goal,
            dailyCalories: preloadedData.dailyCalories,
            remainingCalories: preloadedData.dailyCalories
          }));
          setIsLoggedIn(true);
          setIsLoadingUser(false);
          sessionStorage.removeItem('tempUserData');
          console.log('✅ Using preloaded user data:', preloadedData.name);
          return;
        } catch (error) {
          console.error('Error parsing preloaded data:', error);
        }
      }

      setIsLoadingUser(true);

      try {
        const response = await fetch('/api/session', {
          method: 'GET',
          credentials: 'include'
        });

        if (response.ok) {
          const data = await response.json();

          if (data.currentSession && data.currentSession.user) {
            const user = data.currentSession.user;

            // Update user data
            const dailyCaloriesTarget = user.tdee || 2200;
            setUserData(prev => ({
              ...prev,
              name: user.fullName || "ผู้ใช้งาน",
              age: user.age || 30,
              weight: user.weight || 70,
              height: user.height || 175,
              bmi: user.bmi || 22.9,
              goal: user.goal || "health",
              dailyCalories: dailyCaloriesTarget,
              remainingCalories: dailyCaloriesTarget
            }));

            setIsLoggedIn(true);
            setSessionInfo(data.currentSession);
            setLoginHistory(data.loginHistory || []);

            console.log('✅ Session authenticated:', user.fullName);
          } else {
            setIsLoggedIn(false);
            console.log('❌ No active session');
          }
        } else {
          setIsLoggedIn(false);
          console.log('❌ Session check failed');
          window.location.href = '/login';
          return;
        }
      } catch (error) {
        console.error('❌ Session check error:', error);
        setIsLoggedIn(false);
        window.location.href = '/login';
        return;
      } finally {
        setIsLoadingUser(false);
      }
    };

    checkSession();
  }, []);

  // Fetch FFQ sets
  useEffect(() => {
    const fetchSets = async () => {
      try {
        const response = await fetch('/api/ffq/sets');
        if (!response.ok) {
          throw new Error('ไม่สามารถโหลดข้อมูลชุดคำถามได้');
        }
        const data = await response.json();
        
        // Transform data to include additional UI fields
        const transformedSets = data.map((set: any) => ({
          ...set,
          questionsCount: set.questions?.length || 0,
          estimatedTime: Math.max(5, Math.ceil((set.questions?.length || 0) * 0.5)), // Estimate 30 seconds per question
          category: set.name.includes('ผู้สูงอายุ') ? 'ผู้สูงอายุ' :
                   set.name.includes('เด็ก') || set.name.includes('วัยรุ่น') ? 'เด็กและวัยรุ่น' :
                   set.name.includes('เบาหวาน') ? 'ผู้ป่วยเบาหวาน' : 'ทั่วไป'
        }));
        
        setSets(transformedSets);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchSets();
  }, []);

  // Logout handlers - เหมือนหน้าหลัก
  const handleLogoutClick = () => {
    // สามารถเพิ่ม modal confirmation ได้
    handleLogoutConfirm();
  };

  const handleLogoutConfirm = async () => {
    try {
      await fetch('/api/login', {
        method: 'DELETE',
        credentials: 'include'
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoggedIn(false);
      setSessionInfo(null);
      setLoginHistory([]);
      window.location.href = '/login';
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      'ทั่วไป': 'from-blue-400 to-blue-600',
      'ผู้สูงอายุ': 'from-purple-400 to-purple-600',
      'เด็กและวัยรุ่น': 'from-pink-400 to-rose-500',
      'ผู้ป่วยเบาหวาน': 'from-red-400 to-red-600'
    };
    return colors[category as keyof typeof colors] || 'from-gray-400 to-gray-600';
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'ทั่วไป': return <Users className="w-5 h-5" />;
      case 'ผู้สูงอายุ': return <Clock className="w-5 h-5" />;
      case 'เด็กและวัยรุ่น': return <TrendingUp className="w-5 h-5" />;
      case 'ผู้ป่วยเบาหวาน': return <Target className="w-5 h-5" />;
      default: return <FileText className="w-5 h-5" />;
    }
  };

  if (loading || isLoadingUser) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <Header
          userName={userData.name}
          isLoadingUser={isLoadingUser}
          isLoggedIn={isLoggedIn}
          onLogoutClick={handleLogoutClick}
          currentPage="ffq"
        />
        <div className="max-w-6xl mx-auto px-4 pt-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-600 text-lg">กำลังโหลดข้อมูล...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <Header
          userName={userData.name}
          isLoadingUser={isLoadingUser}
          isLoggedIn={isLoggedIn}
          onLogoutClick={handleLogoutClick}
          currentPage="ffq"
        />
        <div className="max-w-6xl mx-auto px-4 pt-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-red-600 text-2xl">!</span>
              </div>
              <p className="text-red-600 text-lg font-medium">เกิดข้อผิดพลาด: {error}</p>
              <button 
                onClick={() => window.location.reload()} 
                className="mt-4 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                ลองใหม่
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Don't render main content if not logged in (will redirect)
  if (!isLoggedIn) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600 text-lg">กำลังนำทางไปหน้าเข้าสู่ระบบ...</p>
          </div>
        </div>
      </div>
    );
  }

  const activeSets = sets.filter(set => set.isActive);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header แสดงข้อมูลผู้ใช้ */}
      <Header
        userName={userData.name}
        isLoadingUser={isLoadingUser}
        isLoggedIn={isLoggedIn}
        onLogoutClick={handleLogoutClick}
        currentPage="ffq"
      />
      
      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Removed Hero Section and Stats */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">เลือกแบบสอบถาม</h2>
          <p className="text-gray-600">กรุณาเลือกแบบสอบถามที่เหมาะสมกับคุณ</p>
        </div>

        {activeSets.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {activeSets.map(set => (
              <div 
                key={set.id} 
                className="group bg-white/70 backdrop-blur-lg rounded-2xl border border-white/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] overflow-hidden"
              >
                {/* Header */}
                <div className={`bg-gradient-to-r ${getCategoryColor(set.category || 'ทั่วไป')} p-6 text-white`}>
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      {getCategoryIcon(set.category || 'ทั่วไป')}
                      <span className="text-sm font-medium bg-white/20 px-3 py-1 rounded-full">
                        {set.category}
                      </span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm opacity-90">คำถาม</div>
                      <div className="text-xl font-bold">{set.questionsCount}</div>
                    </div>
                  </div>
                  <h3 className="text-xl font-bold mb-2">{set.name}</h3>
                </div>

                {/* Content */}
                <div className="p-6">
                  <p className="text-gray-600 text-sm leading-relaxed mb-6">
                    {set.description}
                  </p>

                  {/* Info */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        <span>{set.estimatedTime} นาที</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <FileText className="w-4 h-4" />
                        <span>{set.questionsCount} คำถาม</span>
                      </div>
                    </div>
                    <CheckCircle className="w-5 h-5 text-emerald-500" />
                  </div>

                  {/* Button */}
                  <button 
                    onClick={() => {
                      // Navigate to FFQ submission page
                      window.location.href = `/ffq/${set.id}`;
                    }}
                    className={`w-full bg-gradient-to-r ${getCategoryColor(set.category || 'ทั่วไป')} text-white py-3 px-6 rounded-xl font-medium hover:shadow-lg transition-all duration-200 flex items-center justify-center gap-2 group-hover:scale-105`}
                  >
                    เริ่มทำแบบสอบถาม
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <ClipboardList className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-xl font-medium text-gray-800 mb-2">ไม่พบแบบสอบถาม</h3>
            <p className="text-gray-600">ขณะนี้ยังไม่มีแบบสอบถามที่สามารถทำได้</p>
          </div>
        )}

        {/* Instructions */}
        <div className="mt-12 bg-white/50 backdrop-blur-lg rounded-2xl p-6 border border-white/30">
          <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2 text-lg">
            <FileText className="w-5 h-5 text-blue-600" />
            คำแนะนำการใช้งาน
          </h3>
          <div className="grid md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div className="space-y-2">
              <p>• เลือกแบบสอบถามที่เหมาะสมกับอายุและสภาวะสุขภาพของคุณ</p>
              <p>• ตอบคำถามตามความเป็นจริงของพฤติกรรมการกินในช่วง 3 เดือนที่ผ่านมา</p>
            </div>
            <div className="space-y-2">
              <p>• ใช้เวลาประมาณ 10-20 นาทีในการตอบแบบสอบถาม</p>
              <p>• ระบบจะให้คำแนะนำโภชนาการหลังจากการประเมิน</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}