'use client';

import { useState, useEffect, FormEvent, FC } from 'react';

// --- TYPE DEFINITIONS ---
interface FFQQuestion {
  id: number;
  question: string;
  foodGroup: string;
  order: number;
  options: string[];
  type: 'single' | 'multiple' | 'frequency';
  ffqSetId: number;
}

interface FFQSet {
  id: number;
  name: string;
  description: string | null;
  isActive: boolean;
  questions: FFQQuestion[];
}

type ModalMode = 'createSet' | 'editSet' | 'createQuestion' | 'editQuestion' | null;

// --- MAIN COMPONENT ---
const AdminFFQQuestions: FC = () => {
  const [sets, setSets] = useState<FFQSet[]>([]);
  const [selectedSet, setSelectedSet] = useState<FFQSet | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [modalMode, setModalMode] = useState<ModalMode>(null);
  const [currentItem, setCurrentItem] = useState<FFQSet | FFQQuestion | null>(null);

  // --- DATA FETCHING ---
  const fetchSets = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/ffq/sets');
      if (!response.ok) throw new Error('ไม่สามารถดึงข้อมูลชุดคำถามได้');
      const data: FFQSet[] = await response.json();
      setSets(data);
      if (data.length > 0) {
        // If a set was previously selected, try to find it again, otherwise default to the first one.
        const currentSelected = data.find(s => s.id === selectedSet?.id) || data[0];
        setSelectedSet(currentSelected);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'เกิดข้อผิดพลาดที่ไม่รู้จัก');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSets();
  }, []);

  // --- EVENT HANDLERS ---
  const handleOpenModal = (mode: ModalMode, item: FFQSet | FFQQuestion | null = null) => {
    setModalMode(mode);
    setCurrentItem(item);
  };

  const handleCloseModal = () => {
    setModalMode(null);
    setCurrentItem(null);
  };

  const handleSave = async (data: Partial<FFQSet | FFQQuestion>) => {
    let url = '';
    let method = 'POST';

    switch (modalMode) {
      case 'createSet':
        url = '/api/ffq/sets';
        break;
      case 'editSet':
        url = `/api/ffq/sets/${currentItem?.id}`;
        method = 'PUT';
        break;
      case 'createQuestion':
        url = '/api/ffq/questions';
        data = { ...data, ffqSetId: selectedSet?.id };
        break;
      case 'editQuestion':
        url = `/api/ffq/questions/${currentItem?.id}`;
        method = 'PUT';
        break;
    }

    try {
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'ไม่สามารถบันทึกข้อมูลได้');
      }
      await fetchSets(); // Refresh data
      handleCloseModal();
    } catch (err) {
      alert(err instanceof Error ? err.message : 'เกิดข้อผิดพลาด');
    }
  };

  const handleDelete = async (type: 'set' | 'question', id: number) => {
    const confirmation = confirm(`คุณแน่ใจหรือไม่ว่าต้องการลบรายการนี้?`);
    if (!confirmation) return;

    const url = type === 'set' ? `/api/ffq/sets/${id}` : `/api/ffq/questions/${id}`;

    try {
      const response = await fetch(url, { method: 'DELETE' });
      if (!response.ok) throw new Error('ไม่สามารถลบข้อมูลได้');
      await fetchSets();
      if (type === 'set' && selectedSet?.id === id) {
        setSelectedSet(sets.length > 1 ? sets[0] : null);
      }
    } catch (err) {
      alert(err instanceof Error ? err.message : 'เกิดข้อผิดพลาด');
    }
  };


  // --- RENDER LOGIC ---
  if (loading) return <div className="flex justify-center items-center h-screen">กำลังโหลด...</div>;
  if (error) return <div className="p-4 text-red-500">ข้อผิดพลาด: {error}</div>;

  return (
    <div className="bg-gray-100 min-h-screen font-sans">
      <header className="bg-white shadow-sm p-4 flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">จัดการแบบสอบถาม (FFQ)</h1>
      </header>

      <main className="p-6 grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* --- SETS COLUMN --- */}
        <div className="lg:col-span-1 bg-white rounded-xl shadow-md p-5">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-700">ชุดคำถาม</h2>
            <button onClick={() => handleOpenModal('createSet')} className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">สร้างใหม่</button>
          </div>
          <ul className="space-y-3">
            {sets.map(set => (
              <li key={set.id} onClick={() => setSelectedSet(set)} className={`p-4 border rounded-lg cursor-pointer transition-all ${selectedSet?.id === set.id ? 'bg-blue-50 border-blue-500 shadow-sm' : 'hover:bg-gray-50'}`}>
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-semibold text-gray-800">{set.name}</p>
                    <p className="text-sm text-gray-500">{set.questions.length} คำถาม</p>
                  </div>
                  <div className="flex items-center space-x-2">
                     <button onClick={(e) => { e.stopPropagation(); handleOpenModal('editSet', set); }} className="text-gray-400 hover:text-blue-600">✏️</button>
                     <button onClick={(e) => { e.stopPropagation(); handleDelete('set', set.id); }} className="text-gray-400 hover:text-red-600">🗑️</button>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>

        {/* --- QUESTIONS COLUMN --- */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-md p-5">
          {selectedSet ? (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-700">คำถามสำหรับ: <span className="text-blue-600">{selectedSet.name}</span></h2>
                <button onClick={() => handleOpenModal('createQuestion')} className="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">เพิ่มคำถาม</button>
              </div>
              <ul className="space-y-4">
                {selectedSet.questions.length > 0 ? selectedSet.questions.sort((a,b) => a.order - b.order).map(q => (
                  <li key={q.id} className="bg-gray-50 p-4 rounded-lg border">
                    <div className="flex justify-between items-start">
                        <div>
                            <p className="font-bold text-gray-800">Q{q.order}: {q.question}</p>
                            <p className="text-sm text-gray-600 mt-1">กลุ่ม: {q.foodGroup} | ประเภท: {q.type}</p>
                            {q.options && q.options.length > 0 && (
                                <div className="mt-2 text-sm text-gray-500">
                                    <strong>ตัวเลือก:</strong>
                                    <div className="flex flex-wrap gap-1 mt-1">
                                        {q.options.map((opt, i) => <span key={i} className="bg-gray-200 px-2 py-0.5 rounded-md">{opt}</span>)}
                                    </div>
                                </div>
                            )}
                        </div>
                        <div className="flex items-center space-x-2">
                            <button onClick={() => handleOpenModal('editQuestion', q)} className="text-gray-400 hover:text-blue-600">✏️</button>
                            <button onClick={() => handleDelete('question', q.id)} className="text-gray-400 hover:text-red-600">🗑️</button>
                        </div>
                    </div>
                  </li>
                )) : <p className="text-center text-gray-500 py-8">ยังไม่มีคำถามในชุดนี้</p>}
              </ul>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500">
              <p>กรุณาเลือกชุดคำถามจากด้านซ้าย</p>
            </div>
          )}
        </div>
      </main>

      {modalMode && <FormModal mode={modalMode} item={currentItem} selectedSet={selectedSet} onSave={handleSave} onClose={handleCloseModal} />}
    </div>
  );
};

// --- MODAL COMPONENT ---
interface ModalProps {
  mode: ModalMode;
  item: FFQSet | FFQQuestion | null;
  selectedSet?: FFQSet | null;
  onSave: (data: Partial<FFQSet | FFQQuestion>) => void;
  onClose: () => void;
}

const FormModal: FC<ModalProps> = ({ mode, item, selectedSet, onSave, onClose }) => {
  const isSet = mode === 'createSet' || mode === 'editSet';
  const [formData, setFormData] = useState<Partial<FFQSet & FFQQuestion>>({});
  const [optionInput, setOptionInput] = useState('');

  const handleAddOption = () => {
    if (optionInput.trim() !== '') {
      const currentOptions = (formData.options || []);
      setFormData(prev => ({ ...prev, options: [...currentOptions, optionInput.trim()] }));
      setOptionInput('');
    }
  };

  const handleRemoveOption = (index: number) => {
    const currentOptions = (formData.options || []);
    setFormData(prev => ({ ...prev, options: currentOptions.filter((_, i) => i !== index) }));
  };

  useEffect(() => {
    if (item) {
      setFormData(item);
    } else {
      // Default values for creation
      if (isSet) {
        setFormData({ name: '', description: ''});
      } else {
        const nextOrder = selectedSet ? selectedSet.questions.length + 1 : 1;
        setFormData({ question: '', foodGroup: '', order: nextOrder, type: 'single', options: [] });
      }
    }
  }, [item, mode, isSet, selectedSet]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const isCheckbox = type === 'checkbox';
    // @ts-ignore
    setFormData(prev => ({ ...prev, [name]: isCheckbox ? e.target.checked : value }));
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  const title = 
      mode === 'createSet' ? 'สร้างชุดคำถามใหม่' :
      mode === 'editSet' ? 'แก้ไขชุดคำถาม' :
      mode === 'createQuestion' ? 'สร้างคำถามใหม่' :
      'แก้ไขคำถาม';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white rounded-xl shadow-2xl p-8 w-full max-w-lg m-4">
        <h3 className="text-2xl font-bold text-gray-800 mb-6">{title}</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          {isSet ? (
            // --- SET FORM ---
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700">ชื่อชุดคำถาม</label>
                <input type="text" name="name" value={(formData as Partial<FFQSet>).name || ''} onChange={handleChange} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">คำอธิบาย</label>
                <textarea name="description" value={(formData as Partial<FFQSet>).description || ''} onChange={handleChange} rows={3} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
              </div>
            </>
          ) : (
            // --- QUESTION FORM ---
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700">คำถาม</label>
                <input type="text" name="question" value={(formData as Partial<FFQQuestion>).question || ''} onChange={handleChange} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">กลุ่มอาหาร (Food Group)</label>
                <input type="text" name="foodGroup" value={(formData as Partial<FFQQuestion>).foodGroup || ''} onChange={handleChange} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">ลำดับ</label>
                <input type="number" name="order" value={(formData as Partial<FFQQuestion>).order || 1} onChange={handleChange} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required />
              </div>
               <div>
                <label className="block text-sm font-medium text-gray-700">ประเภทคำถาม</label>
                <select name="type" value={(formData as Partial<FFQQuestion>).type || 'single'} onChange={handleChange} className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="single">Single Choice</option>
                    <option value="multiple">Multiple Choice</option>
                    <option value="frequency">Frequency</option>
                </select>
              </div>
              {/* --- OPTIONS MANAGEMENT --- */}
              <div>
                <label className="block text-sm font-medium text-gray-700">ตัวเลือกคำตอบ</label>
                <div className="flex items-center gap-2 mt-1">
                    <input
                        type="text"
                        value={optionInput}
                        onChange={(e) => setOptionInput(e.target.value)}
                        onKeyDown={(e) => { if (e.key === 'Enter') { e.preventDefault(); handleAddOption(); } }}
                        className="flex-grow block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="เพิ่มตัวเลือกแล้วกด Enter..."
                    />
                    <button
                        type="button"
                        onClick={handleAddOption}
                        className="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors text-sm font-medium"
                    >
                        เพิ่ม
                    </button>
                </div>
                <div className="flex flex-wrap gap-2 mt-3">
                    {((formData as Partial<FFQQuestion>).options || []).map((opt, index) => (
                        <span key={index} className="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full flex items-center gap-2">
                            {opt}
                            <button
                                type="button"
                                onClick={() => handleRemoveOption(index)}
                                className="text-blue-600 hover:text-blue-800 font-bold"
                            >
                                &times;
                            </button>
                        </span>
                    ))}
                </div>
              </div>
            </>
          )}
          <div className="flex justify-end space-x-3 pt-4">
            <button type="button" onClick={onClose} className="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors">ยกเลิก</button>
            <button type="submit" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">บันทึก</button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AdminFFQQuestions;