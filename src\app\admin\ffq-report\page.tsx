"use client";

import { useEffect, useState } from "react";

export default function AdminFFQReportPage() {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReport = async () => {
      setLoading(true);
      try {
        const res = await fetch("/api/ffq-report");
        if (!res.ok) throw new Error("โหลดรายงานไม่สำเร็จ");
        const d = await res.json();
        setData(d);
      } catch (e: any) {
        setError(e.message || "เกิดข้อผิดพลาด");
      } finally {
        setLoading(false);
      }
    };
    fetchReport();
  }, []);

  if (loading) return <div className="p-8 text-center">กำลังโหลดรายงาน...</div>;
  if (error) return <div className="p-8 text-center text-red-600">{error}</div>;
  if (!data) return null;

  // สรุปภาพรวม
  const { summary, responses } = data;
  const users = Array.from(new Set(responses.map((r: any) => r.userId)));
  const questions = Array.from(new Set(responses.map((r: any) => r.questionId)));

  return (
    <div className="max-w-5xl mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">รายงานการตอบแบบสอบถาม FFQ (Admin)</h1>
      <h2 className="text-lg font-semibold mt-6 mb-2">สรุปภาพรวม (จำนวนผู้ตอบแต่ละตัวเลือก)</h2>
      <div className="overflow-x-auto mb-8">
        <table className="min-w-full border text-xs md:text-sm">
          <thead>
            <tr>
              <th className="border px-2 py-1">รหัสคำถาม</th>
              <th className="border px-2 py-1">ตัวเลือก</th>
              <th className="border px-2 py-1">จำนวน</th>
            </tr>
          </thead>
          <tbody>
            {Object.entries(summary).map(([qid, freqObj]: any) =>
              Object.entries(freqObj).map(([freq, count]: any, i) => (
                <tr key={qid + freq}>
                  <td className="border px-2 py-1">{qid}</td>
                  <td className="border px-2 py-1">{freq}</td>
                  <td className="border px-2 py-1">{count}</td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      <h2 className="text-lg font-semibold mt-6 mb-2">รายละเอียดการตอบแต่ละบุคคล</h2>
      <div className="overflow-x-auto">
        <table className="min-w-full border text-xs md:text-sm">
          <thead>
            <tr>
              <th className="border px-2 py-1">User</th>
              <th className="border px-2 py-1">Question</th>
              <th className="border px-2 py-1">Answer</th>
              <th className="border px-2 py-1">Portion</th>
            </tr>
          </thead>
          <tbody>
            {responses.map((r: any) => (
              <tr key={r.id}>
                <td className="border px-2 py-1">{r.user?.fullName || r.userId}</td>
                <td className="border px-2 py-1">{r.question?.question || r.questionId}</td>
                <td className="border px-2 py-1">{r.frequency}</td>
                <td className="border px-2 py-1">{r.portion || '-'}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
