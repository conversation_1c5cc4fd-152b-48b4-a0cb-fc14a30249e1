import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function cleanup() {
  try {
    // ใช้ raw SQL เพื่อลบข้อมูลแบบ force
    await prisma.$executeRaw`DELETE FROM "FFQAnswer"`;
    await prisma.$executeRaw`DELETE FROM "FFQSubmission"`;
    await prisma.$executeRaw`DELETE FROM "FFQQuestion"`;
    await prisma.$executeRaw`DELETE FROM "FFQSet"`;
    
    console.log('✅ All FFQ data cleaned up!');
  } catch (error) {
    console.log('Cleanup error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanup();