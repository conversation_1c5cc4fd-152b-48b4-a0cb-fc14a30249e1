import { PrismaClient } from '@prisma/client';
import { NextResponse } from 'next/server';

const prisma = new PrismaClient();

// PUT (update) a single FFQ Question by ID
export async function PUT(request: Request, { params }: { params: { questionId: string } }) {
  try {
    const body = await request.json();
    const { question, foodGroup, order, options, type, isActive } = body;

    const updatedQuestion = await prisma.fFQQuestion.update({
      where: { id: parseInt(params.questionId, 10) },
      data: {
        question,
        foodGroup,
        order,
        options,
        type,
      },
    });

    return NextResponse.json(updatedQuestion);
  } catch (error) {
    console.error(`Error updating FFQ question ${params.questionId}:`, error);
    return NextResponse.json({ error: 'Failed to update FFQ question' }, { status: 500 });
  }
}

// DELETE a single FFQ Question by ID
export async function DELETE(request: Request, { params }: { params: { questionId: string } }) {
  try {

    await prisma.fFQQuestion.delete({
      where: { id: parseInt(params.questionId, 10) },
    });

    return new NextResponse(null, { status: 204 }); // No Content
  } catch (error) {
    console.error(`Error deleting FFQ question ${params.questionId}:`, error);
    return NextResponse.json({ error: 'Failed to delete FFQ question' }, { status: 500 });
  }
}
